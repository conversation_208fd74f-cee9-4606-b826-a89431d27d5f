/**
 * Profile Completion Tracking System
 * 
 * Manages profile completion progress, calculates completion percentage,
 * and handles completion rewards and incentives.
 * 
 * Features:
 * - Profile completion percentage calculation
 * - Section-based completion tracking
 * - Automatic points rewards
 * - Progress persistence
 * - Completion suggestions
 * 
 * <AUTHOR> Team
 */

import { 
  doc, 
  updateDoc, 
  getDoc, 
  serverTimestamp 
} from 'firebase/firestore'
import { db } from './firebase'
import { UserProfile, ProfileCompletion, ProfileSection } from '@/types/profile'
import { PointsSystem } from './pointsSystem'

/**
 * Profile completion sections configuration
 */
export const PROFILE_SECTIONS: Record<string, {
  id: string
  name: string
  weight: number
  pointsReward: number
  checkFunction: (profile: UserProfile) => boolean
}> = {
  BASIC_INFO: {
    id: 'basic_info',
    name: 'Basic Information',
    weight: 15,
    pointsReward: 20,
    checkFunction: (profile) => !!(profile.displayName && profile.bio && profile.bio.length > 10)
  },
  AVATAR: {
    id: 'avatar',
    name: 'Profile Picture',
    weight: 15,
    pointsReward: 20,
    checkFunction: (profile) => !!profile.avatar
  },
  CONTACT_PERSONAL: {
    id: 'contact_personal',
    name: 'Contact & Personal Information',
    weight: 25,
    pointsReward: 35,
    checkFunction: (profile) => !!(
      profile.firstName &&
      profile.lastName &&
      profile.phone &&
      profile.emailVerified &&
      profile.dateOfBirth &&
      profile.gender
    )
  },
  LOCATION: {
    id: 'location',
    name: 'Location',
    weight: 10,
    pointsReward: 15,
    checkFunction: (profile) => !!profile.location
  },
  PREFERENCES: {
    id: 'preferences',
    name: 'Preferences Setup',
    weight: 15,
    pointsReward: 20,
    checkFunction: (profile) => !!(
      profile.preferences?.language &&
      profile.preferences?.timezone &&
      profile.preferences?.currency
    )
  },
  NOTIFICATIONS: {
    id: 'notifications',
    name: 'Notification Settings',
    weight: 10,
    pointsReward: 10,
    checkFunction: (profile) => true // Always considered complete as it has defaults
  },
  SOCIAL_LINKS: {
    id: 'social_links',
    name: 'Social Links',
    weight: 15,
    pointsReward: 25,
    checkFunction: (profile) => !!(
      profile.socialLinks &&
      Object.values(profile.socialLinks).some(link => link) ||
      profile.website ||
      profile.isDiscordLinked
    )
  },
  NEWSLETTER: {
    id: 'newsletter',
    name: 'Newsletter Subscription',
    weight: 10,
    pointsReward: 30,
    checkFunction: (profile) => !!profile.preferences?.newsletter
  }
}

/**
 * Profile Completion Manager Class
 */
export class ProfileCompletionManager {
  
  /**
   * Calculate profile completion percentage and sections
   */
  static calculateCompletion(profile: UserProfile): ProfileCompletion {
    // Ensure profile exists
    if (!profile) {
      throw new Error('Profile is required for completion calculation')
    }

    const sections: ProfileSection[] = []
    let totalWeight = 0
    let completedWeight = 0
    for (const [key, config] of Object.entries(PROFILE_SECTIONS)) {
      let isCompleted = false

      try {
        isCompleted = config.checkFunction(profile)
      } catch (error) {
        console.warn(`Error checking completion for section ${config.id}:`, error)
        isCompleted = false
      }

      const existingSection = profile.profileCompletion?.completedSections?.find(s => s.id === config.id)
      
      sections.push({
        id: config.id,
        name: config.name,
        completed: isCompleted,
        pointsAwarded: existingSection?.pointsAwarded || false,
        completedAt: isCompleted && !existingSection?.completedAt ? new Date() : existingSection?.completedAt
      })
      
      totalWeight += config.weight
      if (isCompleted) {
        completedWeight += config.weight
      }
    }
    
    const percentage = Math.round((completedWeight / totalWeight) * 100)
    
    return {
      percentage,
      completedSections: sections,
      lastUpdated: new Date()
    }
  }
  
  /**
   * Update profile completion and award points for newly completed sections
   */
  static async updateProfileCompletion(userId: string, profile: UserProfile): Promise<{
    completion: ProfileCompletion
    pointsAwarded: number
    newlyCompleted: ProfileSection[]
  }> {
    const newCompletion = this.calculateCompletion(profile)
    const oldCompletion = profile.profileCompletion
    
    let totalPointsAwarded = 0
    const newlyCompleted: ProfileSection[] = []
    
    // Check for newly completed sections and award points
    for (const section of newCompletion.completedSections) {
      if (section.completed) {
        const oldSection = oldCompletion?.completedSections?.find(s => s.id === section.id)
        const wasAlreadyCompleted = oldSection?.completed
        const pointsAlreadyAwarded = oldSection?.pointsAwarded
        
        // If section is newly completed or points weren't awarded yet
        if (!wasAlreadyCompleted || (wasAlreadyCompleted && !pointsAlreadyAwarded)) {
          const sectionConfig = Object.values(PROFILE_SECTIONS).find(s => s.id === section.id)
          if (sectionConfig ) {
            try {
              // Award points for section completion
              await PointsSystem.addPointTransaction(userId, {
                type: 'earned',
                source: `profile_section_${section.id}`,
                points: sectionConfig.pointsReward,
                description: `Completed profile section: ${section.name}`,
                metadata: { sectionId: section.id, sectionName: section.name }
              })
              
              section.pointsAwarded = true
              totalPointsAwarded += sectionConfig.pointsReward
              
              if (!wasAlreadyCompleted) {
                newlyCompleted.push(section)
              }
            } catch (error) {
              console.error(`Failed to award points for section ${section.id}:`, error)
            }
          }
        }
      }
    }
    
    // Check for profile completion milestone (100%)
    if (newCompletion.percentage === 100 && oldCompletion?.percentage !== 100) {
      try {
        await PointsSystem.awardProfileCompletionBonus(userId)
        totalPointsAwarded += 50 // Profile completion bonus
      } catch (error) {
        console.error('Failed to award profile completion bonus:', error)
      }
    }
    
    // Update profile in Firestore
    try {
      await updateDoc(doc(db, 'profiles', userId), {
        profileCompletion: newCompletion,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Failed to update profile completion:', error)
    }
    
    return {
      completion: newCompletion,
      pointsAwarded: totalPointsAwarded,
      newlyCompleted
    }
  }
  
  /**
   * Get completion suggestions for user
   */
  static getCompletionSuggestions(profile: UserProfile): {
    nextSteps: Array<{
      sectionId: string
      name: string
      description: string
      pointsReward: number
      priority: 'high' | 'medium' | 'low'
    }>
    estimatedPoints: number
  } {
    const completion = this.calculateCompletion(profile)
    const incompleteSections = completion.completedSections.filter(s => !s.completed)
    
    const suggestions = incompleteSections.map(section => {
      const config = Object.values(PROFILE_SECTIONS).find(s => s.id === section.id)!
      
      // Determine priority based on points and importance
      let priority: 'high' | 'medium' | 'low' = 'medium'
      if (config.pointsReward >= 25) priority = 'high'
      else if (config.pointsReward <= 15) priority = 'low'
      
      return {
        sectionId: section.id,
        name: section.name,
        description: this.getSectionDescription(section.id),
        pointsReward: config.pointsReward,
        priority
      }
    }).sort((a, b) => {
      // Sort by priority, then by points
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority]
      }
      return b.pointsReward - a.pointsReward
    })
    
    const estimatedPoints = suggestions.reduce((total, s) => total + s.pointsReward, 0)
    
    return {
      nextSteps: suggestions,
      estimatedPoints
    }
  }
  
  /**
   * Get description for profile section
   */
  private static getSectionDescription(sectionId: string): string {
    const descriptions: Record<string, string> = {
      basic_info: 'Add your first name, last name, and display name',
      avatar: 'Upload a profile picture to personalize your account',
      contact_info: 'Add your phone number and verify your email',
      personal_details: 'Add your date of birth and gender preferences',
      preferences: 'Set your language, timezone, and currency preferences',
      notifications: 'Configure your notification preferences',
      social_connections: 'Connect your Discord account for community features',
      newsletter: 'Subscribe to our newsletter for updates and exclusive offers'
    }
    
    return descriptions[sectionId] || 'Complete this profile section'
  }
  
  /**
   * Check if user should see onboarding wizard
   */
  static shouldShowOnboarding(profile: UserProfile): boolean {
    if (!profile) return false
    if (profile.onboardingCompleted) return false

    try {
      const completion = this.calculateCompletion(profile)
      return completion.percentage < 50 // Show onboarding if less than 50% complete
    } catch (error) {
      console.warn('Error calculating completion for onboarding check:', error)
      return false
    }
  }
  
  /**
   * Mark onboarding as completed
   */
  static async completeOnboarding(userId: string: Promise<void> {
    try {)
      await updateDoc(doc(db, 'profiles', userId), {
        onboardingCompleted: true,
        onboardingStep: null,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Failed to mark onboarding as completed:', error)
      throw error
    }
  }

  /**
   * Ensure profile has required structure for completion calculation
   */
  static ensureProfileStructure(profile: UserProfile: UserProfile {
    // Create a copy to avoid mutating the original
    const safeProfile = { ...profile }

    // Ensure preferences object exists)
    if (!safeProfile.preferences) {
      safeProfile.preferences = {
        emailNotifications: true,
        smsNotifications: false,
        raffleNotifications: true,
        orderUpdates: true,
        marketingEmails: false,
        newsletter: false,
        language: 'en',
        timezone: 'UTC',
        currency: 'USD'
      }
    } else {
      // Fill in missing preference fields
      const defaultPreferences = {
        emailNotifications: true,
        smsNotifications: false,
        raffleNotifications: true,
        orderUpdates: true,
        marketingEmails: false,
        newsletter: false,
        language: 'en',
        timezone: 'UTC',
        currency: 'USD'
      }
      safeProfile.preferences = { ...defaultPreferences, ...safeProfile.preferences }
    }

    // Ensure profileCompletion object exists
    if (!safeProfile.profileCompletion) {
      safeProfile.profileCompletion = {
        percentage: 0,
        completedSections: [],
        lastUpdated: new Date()
      }
    }

    // Ensure achievements array exists
    if (!safeProfile.achievements) {
      safeProfile.achievements = []
    }

    return safeProfile
  }
}

/**
 * Export completion constants
 */
export const COMPLETION_MILESTONES = {
  BASIC: 25,      // Basic info completed
  INTERMEDIATE: 50, // Half profile completed
  ADVANCED: 75,    // Most profile completed
  COMPLETE: 100    // Full profile completed
}

export const MILESTONE_REWARDS = {
  [COMPLETION_MILESTONES.BASIC]: 25,
  [COMPLETION_MILESTONES.INTERMEDIATE]: 50,
  [COMPLETION_MILESTONES.ADVANCED]: 75,
  [COMPLETION_MILESTONES.COMPLETE]: 100
}
