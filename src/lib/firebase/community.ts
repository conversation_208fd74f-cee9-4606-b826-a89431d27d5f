/**
 * Firebase Community Service
 * 
 * Handles all Firebase operations for community features including
 * real-time subscriptions, data management, and synchronization.
 * 
 * <AUTHOR> Team
 */

import { 
  collection, 
  doc, 
  getDocs, 
  getDoc,
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  onSnapshot,
  Timestamp,
  serverTimestamp,
  increment,
  arrayUnion,
  arrayRemove
} from 'firebase/firestore'
import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject 
} from 'firebase/storage'
import { db, storage } from '../firebase'

// Types
export interface CommunityUser {
  id: string
  username: string
  avatar?: string
  level: number
  points: number
  badges: string[]
  joinDate: Date
  lastActive: Date
  stats: {
    submissions: number
    discussions: number
    votes: number
    likes: number
  }
}

export interface ActivityItem {
  id: string
  type: 'like' | 'comment' | 'submission' | 'achievement' | 'join' | 'challenge' | 'vote' | 'share'
  userId: string
  user: {
    id: string
    name: string
    avatar?: string
    level: number
  }
  content: {
    title: string
    description?: string
    image?: string
    url?: string
  }
  target?: {
    type: 'submission' | 'discussion' | 'challenge' | 'user'
    id: string
    title: string
  }
  metadata?: {
    points?: number
    badge?: string
    category?: string
    difficulty?: string
  }
  timestamp: Date
  engagement: {
    likes: number
    comments: number
    shares: number
  }
}

export interface VoteItem {
  id: string
  title: string
  description: string
  authorId: string
  author: {
    id: string
    name: string
    avatar?: string
    level: number
  }
  category: string
  tags: string[]
  votes: {
    up: number
    down: number
    userVotes: { [userId: string]: 'up' | 'down' }
  }
  status: 'voting' | 'approved' | 'rejected' | 'in_progress' | 'completed'
  deadline?: Date
  createdAt: Date
  updatedAt: Date
}

export interface LeaderboardEntry {
  id: string
  userId: string
  userName: string
  userAvatar?: string
  points: number
  level: number
  rank: number
  change: number
  badges: string[]
  streak: number
  isCurrentUser?: boolean
  period: 'weekly' | 'monthly' | 'alltime'
  lastUpdated: Date
}

export interface Challenge {
  id: string
  title: string
  description: string
  category: string
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  status: 'upcoming' | 'active' | 'completed' | 'cancelled'
  startDate: Date
  endDate: Date
  bannerImage?: string
  rewards: {
    points: number
    badges: string[]
    prizes?: string[]
  }
  requirements: string[]
  submissions: number
  participants: number
  createdBy: {
    id: string
    name: string
    avatar?: string
  }
  createdAt: Date
  updatedAt: Date
}

export interface Submission {
  id: string
  challengeId?: string
  title: string
  description: string
  authorId: string
  author: {
    id: string
    name: string
    avatar?: string
    level: number
  }
  images: string[]
  category: string
  tags: string[]
  likes: number
  views: number
  comments: number
  featured: boolean
  submittedAt: Date
  status: 'pending' | 'approved' | 'featured' | 'rejected'
}

export interface Discussion {
  id: string
  title: string
  content: string
  authorId: string
  author: {
    id: string
    name: string
    avatar?: string
    level: number
  }
  category: string
  tags: string[]
  replies: number
  views: number
  isLocked: boolean
  isPinned: boolean
  isHot: boolean
  lastActivity: Date
  createdAt: Date
  updatedAt: Date
}

export interface SpotlightMember {
  id: string
  name: string
  avatar?: string
  level: number
  achievements: string[]
  joinDate: Date
  bio?: string
  recentWork?: Submission[]
}

/**
 * Activity Feed Service
 */
export class ActivityFeedService {
  private static instance: ActivityFeedService
  private unsubscribes: (() => void)[] = []

  static getInstance(): ActivityFeedService {
    if (!ActivityFeedService.instance) {
      ActivityFeedService.instance = new ActivityFeedService()
    }
    return ActivityFeedService.instance
  }

  /**
   * Subscribe to real-time activity updates
   */
  subscribeToActivities(
    callback: (activities: ActivityItem[]) => void,
    options: {
      limit?: number
      sort?: 'recent' | 'popular' | 'trending'
      userId?: string
    } = {}
  ): () => void {
    if (!db) {
      console.warn('Firebase not initialized, using fallback data')
      callback([])
      return () => {}
    }

    const { limit: limitCount = 50, sort = 'recent', userId } = options
    
    let q = query(
      collection(db, 'activities'),
      orderBy(sort === 'recent' ? 'timestamp' : 'engagement.likes', 'desc'),
      limit(limitCount)
    )

    if (userId) {
      q = query(
        collection(db, 'activities'),
        where('userId', '==', userId),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      )
    }

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const activities = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date()
      })) as ActivityItem[]

      callback(activities)
    }, (error) => {
      console.error('Error subscribing to activities:', error)
      // Provide fallback data
      callback([])
    })

    this.unsubscribes.push(unsubscribe)
    return unsubscribe
  }

  /**
   * Add new activity
   */
  async addActivity(activity: Omit<ActivityItem, 'id' | 'timestamp'>): Promise<string> {
    if (!db) {
      throw new Error('Firebase not initialized')
    }

    try {
      const docRef = await addDoc(collection(db, 'activities'), {
        ...activity,
        timestamp: serverTimestamp(),
        engagement: {
          likes: 0,
          comments: 0,
          shares: 0,
          ...activity.engagement
        }
      })
      return docRef.id
    } catch (error) {
      console.error('Error adding activity:', error)
      throw error
    }
  }

  /**
   * Update activity engagement
   */
  async updateEngagement(
    activityId: string, 
    type: 'likes' | 'comments' | 'shares', 
    incrementValue: boolean = true
  ): Promise<void> {
    if (!db) {
      throw new Error('Firebase not initialized')
    }

    try {
      const activityRef = doc(db, 'activities', activityId)
      await updateDoc(activityRef, {
        [`engagement.${type}`]: increment(incrementValue ? 1 : -1)
      })
    } catch (error) {
      console.error('Error updating engagement:', error)
      throw error
    }
  }

  /**
   * Get activities (one-time fetch)
   */
  async getActivities(options: {
    limit?: number
    sort?: 'recent' | 'popular' | 'trending'
    userId?: string
  } = {}): Promise<ActivityItem[]> {
    if (!db) {
      console.warn('Firebase not initialized, returning empty array')
      return []
    }

    try {
      const { limit: limitCount = 50, sort = 'recent', userId } = options
      
      let q = query(
      collection(db, 'activities'),
        orderBy(sort === 'recent' ? 'timestamp' : 'engagement.likes', 'desc'),
        limit(limitCount)
      )

      if (userId) {
        q = query(
      collection(db, 'activities'),
          where('userId', '==', userId),
          orderBy('timestamp', 'desc'),
          limit(limitCount)
        )
      }

      const snapshot = await getDocs(q)
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date()
      })) as ActivityItem[]
    } catch (error) {
      console.error('Error fetching activities:', error)
      return []
    }
  }

  /**
   * Clean up all subscriptions
   */
  cleanup(): void {
    this.unsubscribes.forEach(unsubscribe => unsubscribe())
    this.unsubscribes = []
  }
}

/**
 * Voting Service
 */
export class VotingService {
  private static instance: VotingService
  private unsubscribes: (() => void)[] = []

  static getInstance(): VotingService {
    if (!VotingService.instance) {
      VotingService.instance = new VotingService()
    }
    return VotingService.instance
  }

  /**
   * Subscribe to real-time vote updates
   */
  subscribeToVotes(
    callback: (votes: VoteItem[]) => void,
    options: {
      category?: string
      status?: string
      sort?: 'trending' | 'newest' | 'votes'
      limit?: number
    } = {}
  ): () => void {
    if (!db) {
      console.warn('Firebase not initialized, using fallback data')
      callback([])
      return () => {}
    }

    const { category, status, sort = 'trending', limit: limitCount = 20 } = options
    
    let q = query(
      collection(db, 'votes'),
      orderBy(sort === 'newest' ? 'createdAt' : 'votes.up', 'desc'),
      limit(limitCount)
    )

    if (category) {
      q = query(
      collection(db, 'votes'),
        where('category', '==', category),
        orderBy(sort === 'newest' ? 'createdAt' : 'votes.up', 'desc'),
        limit(limitCount)
      )
    }

    if (status) {
      q = query(
      collection(db, 'votes'),
        where('status', '==', status),
        orderBy(sort === 'newest' ? 'createdAt' : 'votes.up', 'desc'),
        limit(limitCount)
      )
    }

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const votes = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        deadline: doc.data().deadline?.toDate()
      })) as VoteItem[]

      callback(votes)
    }, (error) => {
      console.error('Error subscribing to votes:', error)
      callback([])
    })

    this.unsubscribes.push(unsubscribe)
    return unsubscribe
  }

  /**
   * Submit a vote
   */
  async submitVote(voteId: string, userId: string, voteType: 'up' | 'down'): Promise<void> {
    if (!db) {
      throw new Error('Firebase not initialized')
    }

    try {
      const voteRef = doc(db, 'votes', voteId)
      const voteDoc = await getDoc(voteRef)
      
      if (!voteDoc.exists()) {
        throw new Error('Vote not found')
      }

      const currentVotes = voteDoc.data().votes || { up: 0, down: 0, userVotes: {} }
      const previousVote = currentVotes.userVotes[userId]

      // Calculate vote changes
      let upChange = 0
      let downChange = 0

      if (previousVote === voteType) {
        // User is removing their vote
        if (voteType === 'up') upChange = -1
        else downChange = -1
        delete currentVotes.userVotes[userId]
      } else {
        // User is changing or adding their vote
        if (previousVote === 'up') upChange = -1
        else if (previousVote === 'down') downChange = -1
        
        if (voteType === 'up') upChange += 1
        else downChange += 1
        
        currentVotes.userVotes[userId] = voteType
      }

      await updateDoc(voteRef, {
        'votes.up': increment(upChange),
        'votes.down': increment(downChange),
        'votes.userVotes': currentVotes.userVotes,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Error submitting vote:', error)
      throw error
    }
  }

  /**
   * Clean up all subscriptions
   */
  cleanup(): void {
    this.unsubscribes.forEach(unsubscribe => unsubscribe())
    this.unsubscribes = []
  }
}

/**
 * Leaderboard Service
 */
export class LeaderboardService {
  private static instance: LeaderboardService
  private unsubscribes: (() => void)[] = []

  static getInstance(): LeaderboardService {
    if (!LeaderboardService.instance) {
      LeaderboardService.instance = new LeaderboardService()
    }
    return LeaderboardService.instance
  }

  /**
   * Subscribe to real-time leaderboard updates
   */
  subscribeToLeaderboard(
    callback: (entries: LeaderboardEntry[]) => void,
    period: 'weekly' | 'monthly' | 'alltime' = 'weekly',
    limitCount: number = 50
  ): () => void {
    if (!db ) {
      console.warn('Firebase not initialized, using fallback data')
      callback([])
      return () => {}
    }

    const q = query(
      collection(db, 'leaderboard'),
      where('period', '==', period),
      orderBy('rank', 'asc'),
      limit(limitCount)
    )

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const entries = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        lastUpdated: doc.data().lastUpdated?.toDate() || new Date()
      })) as LeaderboardEntry[]

      callback(entries)
    }, (error) => {
      console.error('Error subscribing to leaderboard:', error)
      callback([])
    })

    this.unsubscribes.push(unsubscribe)
    return unsubscribe
  }

  /**
   * Clean up all subscriptions
   */
  cleanup(): void {
    this.unsubscribes.forEach(unsubscribe => unsubscribe())
    this.unsubscribes = []
  }
}

/**
 * Community Stats Service
 */
export class CommunityStatsService {
  private static instance: CommunityStatsService

  static getInstance(): CommunityStatsService {
    if (!CommunityStatsService.instance) {
      CommunityStatsService.instance = new CommunityStatsService()
    }
    return CommunityStatsService.instance
  }

  /**
   * Get community stats
   */
  async getCommunityStats(): Promise<{
    totalMembers: number
    activeChallenges: number
    onlineUsers: number
    totalSubmissions: number
    weeklyGrowth: number
  }> {
    if (!db) {
      console.warn('Firebase not initialized, returning fallback stats')
      return {
        totalMembers: 2847,
        activeChallenges: 12,
        onlineUsers: 234,
        totalSubmissions: 1856,
        weeklyGrowth: 15
      }
    }

    try {
      // Get global stats from community_stats collection
      const statsDoc = await getDoc(doc(db, 'community_stats', 'global'))
      
      if (statsDoc.exists()) {
        const data = statsDoc.data()
        return {
          totalMembers: data.totalUsers || 0,
          activeChallenges: data.totalChallenges || 0,
          onlineUsers: data.activeUsers || 0,
          totalSubmissions: data.totalSubmissions || 0,
          weeklyGrowth: 15 // This would be calculated from growth data
        }
      } else {
        // Fallback: calculate stats from individual collections
        const [challengesSnapshot, submissionsSnapshot, membersSnapshot] = await Promise.all([
          getDocs(query(collection(db, 'challenges'), where('status', '==', 'active'))),
          getDocs(collection(db, 'submissions')),
          getDocs(collection(db, 'community_members'))
        ])

        return {
          totalMembers: membersSnapshot.size,
          activeChallenges: challengesSnapshot.size,
          onlineUsers: Math.floor(membersSnapshot.size * 0.1), // Estimate 10% online
          totalSubmissions: submissionsSnapshot.size,
          weeklyGrowth: 15 // Would be calculated from historical data
        }
      }
    } catch (error) {
      console.error('Error fetching community stats:', error)
      // Return fallback data
      return {
        totalMembers: 2847,
        activeChallenges: 12,
        onlineUsers: 234,
        totalSubmissions: 1856,
        weeklyGrowth: 15
      }
    }
  }

  /**
   * Subscribe to real-time community stats updates
   */
  subscribeToStats(
    callback: (stats: {
      totalMembers: number
      activeChallenges: number
      onlineUsers: number
      totalSubmissions: number
      weeklyGrowth: number
    }) => void
  ): () => void {
    if (!db) {
      console.warn('Firebase not initialized, using fallback data')
      callback({
        totalMembers: 2847,
        activeChallenges: 12,
        onlineUsers: 234,
        totalSubmissions: 1856,
        weeklyGrowth: 15
      })
      return () => {}
    }

    const unsubscribe = onSnapshot(
      doc(db, 'community_stats', 'global'), 
      (doc) => {
        if (doc.exists()) {
          const data = doc.data()
          callback({
            totalMembers: data.totalUsers || 0,
            activeChallenges: data.totalChallenges || 0,
            onlineUsers: data.activeUsers || 0,
            totalSubmissions: data.totalSubmissions || 0,
            weeklyGrowth: 15 // This would be calculated from growth data
          })
        } else {
          // Fallback to calculating from individual collections
          this.getCommunityStats().then(callback)
        }
      },
      (error) => {
        console.error('Error subscribing to community stats:', error)
        // Provide fallback data
        callback({
          totalMembers: 2847,
          activeChallenges: 12,
          onlineUsers: 234,
          totalSubmissions: 1856,
          weeklyGrowth: 15
        })
      }
    )

    return unsubscribe
  }
}

/**
 * Featured Content Service
 */
export class FeaturedContentService {
  private static instance: FeaturedContentService

  static getInstance(): FeaturedContentService {
    if (!FeaturedContentService.instance) {
      FeaturedContentService.instance = new FeaturedContentService()
    }
    return FeaturedContentService.instance
  }

  /**
   * Get featured submissions
   */
  async getFeaturedSubmissions(limitCount: number = 6): Promise<Submission[]> {
    if (!db ) {
      console.warn('Firebase not initialized, returning fallback featured submissions')
      return this.getFallbackSubmissions()
    }

    try {
      const q = query(
      collection(db, 'submissions'),
        where('featured', '==', true),
        where('status', '==', 'approved'),
        orderBy('submittedAt', 'desc'),
        limit(limitCount)
      )

      const snapshot = await getDocs(q)
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        submittedAt: doc.data().submittedAt?.toDate() || new Date()
      })) as Submission[]
    } catch (error) {
      console.error('Error fetching featured submissions:', error)
      return this.getFallbackSubmissions()
    }
  }

  /**
   * Get trending discussions
   */
  async getTrendingDiscussions(limitCount: number = 5): Promise<Discussion[]> {
    if (!db ) {
      console.warn('Firebase not initialized, returning fallback discussions')
      return this.getFallbackDiscussions()
    }

    try {
      const q = query(
      collection(db, 'discussions'),
        where('isHot', '==', true),
        orderBy('lastActivity', 'desc'),
        limit(limitCount)
      )

      const snapshot = await getDocs(q)
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        lastActivity: doc.data().lastActivity?.toDate() || new Date(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as Discussion[]
    } catch (error) {
      console.error('Error fetching trending discussions:', error)
      return this.getFallbackDiscussions()
    }
  }

  private getFallbackSubmissions(): Submission[] {
    return [
      {
        id: '1',
        title: 'Cosmic Nebula Keycap',
        description: 'Hand-crafted artisan keycap inspired by cosmic nebulae',
        authorId: '1',
        author: { id: '1', name: 'SpaceTyper', level: 15 },
        images: ['/images/submissions/cosmic-nebula.jpg'],
        category: 'Artisan Keycap',
        tags: ['artisan', 'space', 'resin'],
        likes: 234,
        views: 1250,
        comments: 45,
        featured: true,
        submittedAt: new Date('2024-01-15'),
        status: 'featured'
      },
      {
        id: '2',
        title: 'Retro Gaming Setup',
        description: 'Complete vintage gaming setup with custom keyboard',
        authorId: '2',
        author: { id: '2', name: 'PixelMaster', level: 12 },
        images: ['/images/submissions/retro-gaming.jpg'],
        category: 'Setup Showcase',
        tags: ['setup', 'retro', 'gaming'],
        likes: 189,
        views: 890,
        comments: 23,
        featured: true,
        submittedAt: new Date('2024-01-14'),
        status: 'featured'
      }
    ]
  }
  private getFallbackDiscussions(): Discussion[] {
    return [
      {
        id: '1',
        title: 'Best switches for gaming keyboards?',
        content: 'Looking for recommendations on the best switches for competitive gaming...',
        authorId: '1',
        author: { id: '1', name: 'GamerTypist', level: 8 },
        category: 'Hardware',
        tags: ['switches', 'gaming'],
        replies: 45,
        views: 1200,
        isLocked: false,
        isPinned: false,
        isHot: true,
        lastActivity: new Date(),
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date()
      },
      {
        id: '2',
        title: 'Custom cable recommendations',
        content: 'Share your favorite custom cable makers and designs...',
        authorId: '2',
        author: { id: '2', name: 'CableEnthusiast', level: 12 },
        category: 'Accessories',
        tags: ['cables', 'custom'],
        replies: 23,
        views: 680,
        isLocked: false,
        isPinned: false,
        isHot: false,
        lastActivity: new Date(),
        createdAt: new Date('2024-01-14'),
        updatedAt: new Date()
      }
    ]
  }
}

/**
 * Submission Upload Service
 */
export class SubmissionUploadService {
  private static instance: SubmissionUploadService

  static getInstance(): SubmissionUploadService {
    if (!SubmissionUploadService.instance) {
      SubmissionUploadService.instance = new SubmissionUploadService()
    }
    return SubmissionUploadService.instance
  }

  /**
   * Upload submission images to Firebase Storage
   */
  async uploadSubmissionImages(
    userId: string, 
    submissionId: string, 
    files: File[]
  ): Promise<string[]> {
    if (!storage) {
      throw new Error('Firebase Storage not initialized')
    }
    const uploadPromises = files.map(async (file, index) => {
      // Validate file type and size
      if (!file.type.startsWith('image/')) {
        throw new Error(`File ${file.name} is not an image`)
      }

      if (file.size > 15 * 1024 * 1024) { // 15MB limit
        throw new Error(`File ${file.name} is too large. Maximum size is 15MB`)
      }

      // Create storage reference
      const fileExtension = file.name.split('.').pop()
      const fileName = `${Date.now()}_${index}.${fileExtension}`
      const storageRef = ref(storage, `community-submissions/${userId}/${submissionId}/${fileName}`)

      try {
        // Upload file
        const snapshot = await uploadBytes(storageRef, file)
        
        // Get download URL
        const downloadURL = await getDownloadURL(snapshot.ref)
        return downloadURL
      } catch (error) {
        console.error(`Error uploading file ${file.name}:`, error)
        throw error
      }
    })

    return Promise.all(uploadPromises)
  }

  /**
   * Create a new submission
   */
  async createSubmission(submissionData: {
    title: string
    description: string
    category: string
    tags: string[]
    images: File[]
    challengeId?: string
    authorId: string
    author: {
      id: string
      name: string
      avatar?: string
      level: number
    }
  }): Promise<string> {
    if (!db) {
      throw new Error('Firebase not initialized')
    }

    try {
      // First create the submission document to get an ID
      const submissionRef = await addDoc(collection(db, 'submissions'), {
        title: submissionData.title,
        description: submissionData.description,
        category: submissionData.category,
        tags: submissionData.tags,
        challengeId: submissionData.challengeId || null,
        authorId: submissionData.authorId,
        author: submissionData.author,
        images: [], // Will be updated after upload
        likes: 0,
        views: 0,
        comments: 0,
        featured: false,
        status: 'pending', // Needs moderation
        submittedAt: serverTimestamp(),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      })

      // Upload images if provided
      let imageUrls: string[] = []
      if (submissionData.images.length > 0) {
        imageUrls = await this.uploadSubmissionImages(
          submissionData.authorId,
          submissionRef.id,
          submissionData.images
        )

        // Update submission with image URLs
        await updateDoc(submissionRef, {
          images: imageUrls,
          updatedAt: serverTimestamp()
        })
      }

      // Create activity entry
      await activityFeedService.addActivity({
        type: 'submission',
        userId: submissionData.authorId,
        user: submissionData.author,
        content: {
          title: `New submission: ${submissionData.title}`,
          description: submissionData.description,
          image: imageUrls[0] || undefined
        },
        target: {
          type: 'submission',
          id: submissionRef.id,
          title: submissionData.title
        },
        metadata: {
          category: submissionData.category
        },
        engagement: {
          likes: 0,
          comments: 0,
          shares: 0
        }
      })

      return submissionRef.id
    } catch (error) {
      console.error('Error creating submission:', error)
      throw error
    }
  }

  /**
   * Update an existing submission
   */
  async updateSubmission(
    submissionId: string,
    updates: Partial<{
      title: string
      description: string
      category: string
      tags: string[]
      newImages: File[]
      removeImageUrls: string[]
    }>,
    userId: string
  ): Promise<void> {
    if (!db) {
      throw new Error('Firebase not initialized')
    }

    try {
      const submissionRef = doc(db, 'submissions', submissionId)
      const submissionDoc = await getDoc(submissionRef)

      if (!submissionDoc.exists()) {
        throw new Error('Submission not found')
      }

      const submissionData = submissionDoc.data()
      
      // Check if user owns this submission
      if (submissionData.authorId !== userId) {
        throw new Error('Unauthorized: You can only edit your own submissions')
      }

      let currentImages = submissionData.images || []

      // Remove specified images)
      if (updates.removeImageUrls && updates.removeImageUrls.length > 0) {
        // Delete from storage
        if (storage) {
          const deletePromises = updates.removeImageUrls.map(async (url) => {
            try {
              const imageRef = ref(storage, url)
              await deleteObject(imageRef)
            } catch (error) {
              console.warn(`Failed to delete image: ${url}`, error)
            }
          })
          await Promise.all(deletePromises)
        }

        // Remove from current images array
        currentImages = currentImages.filter((url: string) => !updates.removeImageUrls!.includes(url))
      }

      // Upload new images
      if (updates.newImages && updates.newImages.length > 0) {
        const newImageUrls = await this.uploadSubmissionImages(
          userId,
          submissionId,
          updates.newImages
        )
        currentImages = [...currentImages, ...newImageUrls]
      }

      // Prepare update data
      const updateData: any = {
        updatedAt: serverTimestamp(),
        images: currentImages
      }
      if (updates.title !== undefined) updateData.title = updates.title
      if (updates.description !== undefined) updateData.description = updates.description
      if (updates.category !== undefined) updateData.category = updates.category
      if (updates.tags !== undefined) updateData.tags = updates.tags

      // Update submission
      await updateDoc(submissionRef, updateData)
    } catch (error) {
      console.error('Error updating submission:', error)
      throw error
    }
  }

  /**
   * Delete a submission
   */
  async deleteSubmission(submissionId: string, userId: string): Promise<void> {
    if (!db) {
      throw new Error('Firebase not initialized')
    }

    try {
      const submissionRef = doc(db, 'submissions', submissionId)
      const submissionDoc = await getDoc(submissionRef)

      if (!submissionDoc.exists()) {
        throw new Error('Submission not found')
      }

      const submissionData = submissionDoc.data()
      
      // Check if user owns this submission
      if (submissionData.authorId !== userId) {
        throw new Error('Unauthorized: You can only delete your own submissions')
      }

      // Delete images from storage
      if (storage && submissionData.images && submissionData.images.length > 0) {
        const deletePromises = submissionData.images.map(async (url: string) => {
          try {
            const imageRef = ref(storage, url)
            await deleteObject(imageRef)
          } catch (error) {
            console.warn(`Failed to delete image: ${url}`, error)
          }
        })
        await Promise.all(deletePromises)
      }

      // Delete submission document
      await deleteDoc(submissionRef)
    } catch (error) {
      console.error('Error deleting submission:', error)
      throw error
    }
  }

  /**
   * Get submission categories
   */
  getSubmissionCategories(): string[] {
    return [
      'Artisan Keycap',
      'Keyboard Build',
      'Setup Showcase',
      'Photography',
      'Modification',
      'Prototype',
      'Tutorial',
      'Other'
    ]
  }

  /**
   * Validate submission data
   */
  validateSubmissionData(data: {
    title: string
    description: string
    category: string
    tags: string[]
    images: File[]
  }): string[] {
    const errors: string[] = []

    if (!data.title || data.title.trim().length < 3) {
      errors.push('Title must be at least 3 characters long')
    }
    if (data.title && data.title.length > 100) {
      errors.push('Title cannot exceed 100 characters')
    }

    if (!data.description || data.description.trim().length < 10) {
      errors.push('Description must be at least 10 characters long')
    }

    if (data.description && data.description.length > 2000) {
      errors.push('Description cannot exceed 2000 characters')
    }

    if (!data.category) {
      errors.push('Category is required')
    }

    if (!this.getSubmissionCategories().includes(data.category)) {
      errors.push('Invalid category selected')
    }

    if (data.tags.length === 0) {
      errors.push('At least one tag is required')
    }

    if (data.tags.length > 10) {
      errors.push('Maximum 10 tags allowed')
    }

    if (data.images.length === 0) {
      errors.push('At least one image is required')
    }

    if (data.images.length > 10) {
      errors.push('Maximum 10 images allowed')
    }

    // Validate each image
    data.images.forEach((file, index) => {
      if (!file.type.startsWith('image/')) {
        errors.push(`File ${index + 1} is not an image`)
      }

      if (file.size > 15 * 1024 * 1024) {
        errors.push(`File ${index + 1} is too large (max 15MB)`)
      }
    })

    return errors
  }
}

/**
 * Community Search Service
 */
export class CommunitySearchService {
  private static instance: CommunitySearchService

  static getInstance(): CommunitySearchService {
    if (!CommunitySearchService.instance) {
      CommunitySearchService.instance = new CommunitySearchService()
    }
    return CommunitySearchService.instance
  }

  /**
   * Search across multiple community collections
   */
  async searchCommunityContent(
    query: string,
    options: {
      types?: ('submissions' | 'discussions' | 'challenges' | 'members')[]
      limit?: number
      category?: string
    } = {}
  ): Promise<{
    submissions: Submission[]
    discussions: Discussion[]
    challenges: Challenge[]
    members: CommunityUser[]
  }> {
    const { types = ['submissions', 'discussions', 'challenges', 'members'], limit = 20 } = options

    if (!db) {
      console.warn('Firebase not initialized, returning empty search results')
      return { submissions: [], discussions: [], challenges: [], members: [] }
    }

    const results = {
      submissions: [] as Submission[],
      discussions: [] as Discussion[],
      challenges: [] as Challenge[],
      members: [] as CommunityUser[]
    }

    try {
      const searchPromises: Promise<void>[] = []

      // Search submissions
      if (types.includes('submissions')) {
        searchPromises.push(
          this.searchSubmissions(query, limit).then(items => { results.submissions = items })
        )
      }

      // Search discussions
      if (types.includes('discussions')) {
        searchPromises.push(
          this.searchDiscussions(query, limit).then(items => { results.discussions = items })
        )
      }

      // Search challenges
      if (types.includes('challenges')) {
        searchPromises.push(
          this.searchChallenges(query, limit).then(items => { results.challenges = items })
        )
      }

      // Search members
      if (types.includes('members')) {
        searchPromises.push(
          this.searchMembers(query, limit).then(items => { results.members = items })
        )
      }

      await Promise.all(searchPromises)
      return results
    } catch (error) {
      console.error('Error performing community search:', error)
      return { submissions: [], discussions: [], challenges: [], members: [] }
    }
  }

  private async searchSubmissions(searchQuery: string, limitCount: number): Promise<Submission[]> {
    // Note: Firestore doesn't support full-text search natively
    // This is a simplified implementation using title matching
    // In production, you would use Algolia, ElasticSearch, or similar
    const q = query(
      collection(db!, 'submissions'),
      where('status', '==', 'approved'),
      orderBy('submittedAt', 'desc'),
      limit(limitCount)
    )

    const snapshot = await getDocs(q)
    const allSubmissions = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      submittedAt: doc.data().submittedAt?.toDate() || new Date()
    })) as Submission[]

    // Client-side filtering for search query
    const normalizedQuery = searchQuery.toLowerCase()
    return allSubmissions.filter(submission =>
      submission.title.toLowerCase().includes(normalizedQuery) ||
      submission.description.toLowerCase().includes(normalizedQuery) ||
      submission.tags.some(tag => tag.toLowerCase().includes(normalizedQuery))
    )
  }

  private async searchDiscussions(searchQuery: string, limitCount: number): Promise<Discussion[]> {
    const q = query(
      collection(db!, 'discussions'),
      orderBy('lastActivity', 'desc'),
      limit(limitCount)
    )

    const snapshot = await getDocs(q)
    const allDiscussions = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      lastActivity: doc.data().lastActivity?.toDate() || new Date(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Discussion[]

    const normalizedQuery = searchQuery.toLowerCase()
    return allDiscussions.filter(discussion =>
      discussion.title.toLowerCase().includes(normalizedQuery) ||
      discussion.content.toLowerCase().includes(normalizedQuery) ||
      discussion.tags.some(tag => tag.toLowerCase().includes(normalizedQuery))
    )
  }

  private async searchChallenges(searchQuery: string, limitCount: number): Promise<Challenge[]> {
    const q = query(
      collection(db!, 'challenges'),
      where('status', 'in', ['active', 'upcoming']),
      orderBy('startDate', 'desc'),
      limit(limitCount)
    )

    const snapshot = await getDocs(q)
    const allChallenges = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      startDate: doc.data().startDate?.toDate() || new Date(),
      endDate: doc.data().endDate?.toDate() || new Date(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Challenge[]

    const normalizedQuery = searchQuery.toLowerCase()
    return allChallenges.filter(challenge =>
      challenge.title.toLowerCase().includes(normalizedQuery) ||
      challenge.description.toLowerCase().includes(normalizedQuery) ||
      challenge.category.toLowerCase().includes(normalizedQuery)
    )
  }

  private async searchMembers(searchQuery: string, limitCount: number): Promise<CommunityUser[]> {
    const q = query(
      collection(db!, 'community_members'),
      orderBy('lastActive', 'desc'),
      limit(limitCount)
    )

    const snapshot = await getDocs(q)
    const allMembers = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      joinDate: doc.data().joinDate?.toDate() || new Date(),
      lastActive: doc.data().lastActive?.toDate() || new Date()
    })) as CommunityUser[]

    const normalizedQuery = searchQuery.toLowerCase()
    return allMembers.filter(member =>
      member.username.toLowerCase().includes(normalizedQuery) ||
      member.badges.some(badge => badge.toLowerCase().includes(normalizedQuery))
    )
  }

  /**
   * Get trending search terms
   */
  async getTrendingSearches(): Promise<string[]> {
    if (!db) {
      return ['artisan keycaps', 'mechanical switches', 'custom builds', 'gaming setup']
    }

    try {
      // In a real implementation, you'd track search analytics
      // For now, return some popular terms
      return ['artisan keycaps', 'mechanical switches', 'custom builds', 'gaming setup', 'soldering', 'pcb design']
    } catch (error) {
      console.error('Error fetching trending searches:', error)
      return ['artisan keycaps', 'mechanical switches', 'custom builds', 'gaming setup']
    }
  }
}

/**
 * Spotlight Service
 */
export class SpotlightService {
  private static instance: SpotlightService

  static getInstance(): SpotlightService {
    if (!SpotlightService.instance) {
      SpotlightService.instance = new SpotlightService()
    }
    return SpotlightService.instance
  }

  /**
   * Get current spotlight member
   */
  async getSpotlightMember(): Promise<SpotlightMember | null> {
    if (!db) {
      console.warn('Firebase not initialized, returning fallback spotlight member')
      return this.getFallbackSpotlightMember()
    }

    try {
      // Get current month's spotlight member
      const currentMonth = new Date().toISOString().slice(0, 7) // YYYY-MM format
      const q = query(
      collection(db, 'spotlight_members'),
        where('period', '==', currentMonth),
        limit(1)
      )

      const snapshot = await getDocs(q)
      if (snapshot.empty) {
        return this.getFallbackSpotlightMember()
      }

      const memberData = snapshot.docs[0].data()
      
      // Get member's recent work
      let recentWork: Submission[] = []
      if (memberData.userId) {
        const submissionsQuery = query(
      collection(db, 'submissions'),
          where('authorId', '==', memberData.userId),
          where('status', '==', 'approved'),
          orderBy('submittedAt', 'desc'),
          limit(3)
        )
        
        const submissionsSnapshot = await getDocs(submissionsQuery)
        recentWork = submissionsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          submittedAt: doc.data().submittedAt?.toDate() || new Date()
        })) as Submission[]
      }

      return {
        id: snapshot.docs[0].id,
        ...memberData,
        joinDate: memberData.joinDate?.toDate() || new Date(),
        recentWork
      } as SpotlightMember
    } catch (error) {
      console.error('Error fetching spotlight member:', error)
      return this.getFallbackSpotlightMember()
    }
  }

  private getFallbackSpotlightMember(): SpotlightMember {
    return {
      id: '1',
      name: 'Alex Rodriguez',
      avatar: '/avatars/alex.jpg',
      level: 25,
      achievements: ['Creator Master', 'Challenge Champion', 'Community Helper'],
      joinDate: new Date('2023-03-15'),
      bio: 'Passionate keyboard enthusiast and designer who loves creating unique artisan keycaps and helping newcomers learn the craft.',
      recentWork: [
        {
          id: '1',
          title: 'Cosmic Nebula Series',
          description: 'Hand-crafted artisan keycap series',
          authorId: '1',
          author: { id: '1', name: 'Alex Rodriguez', level: 25 },
          images: ['/images/submissions/cosmic-nebula.jpg'],
          category: 'Artisan Keycap',
          tags: ['artisan', 'space'],
          likes: 234,
          views: 1250,
          comments: 45,
          featured: true,
          submittedAt: new Date('2024-01-15'),
          status: 'featured'
        }
      ]
    }
  }
}

// Export singleton instances
export const activityFeedService = ActivityFeedService.getInstance()
export const votingService = VotingService.getInstance()
export const leaderboardService = LeaderboardService.getInstance()
export const communityStatsService = CommunityStatsService.getInstance()
export const featuredContentService = FeaturedContentService.getInstance()
export const submissionUploadService = SubmissionUploadService.getInstance()
export const communitySearchService = CommunitySearchService.getInstance()
export const spotlightService = SpotlightService.getInstance()

// Cleanup function to call when app unmounts
export const cleanupCommunityServices = () => {
  activityFeedService.cleanup()
  votingService.cleanup()
  leaderboardService.cleanup()
}