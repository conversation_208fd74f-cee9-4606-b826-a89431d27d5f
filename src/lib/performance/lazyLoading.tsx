/**
 * Lazy Loading Utilities for Performance Optimization
 * 
 * Provides utilities for lazy loading components, images, and other resources
 * to improve initial page load performance and reduce bundle size.
 * 
 * <AUTHOR> Team
 */

import React, { Suspense, lazy, ComponentType } from 'react'
import { motion } from 'framer-motion'

/**
 * Loading spinner component for lazy loaded components
 */
const LoadingSpinner: React.FC<{ message?: string }> = ({ message = 'Loading...' }) => (
  <div className="flex items-center justify-center p-8">
    <div className="text-center">
      <motion.div
        className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full mx-auto mb-3"
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
      />
      <p className="text-gray-400 text-sm">{message}</p>
    </div>
  </div>
)

/**
 * Error boundary for lazy loaded components
 */
class LazyLoadErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy load error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <p className="text-red-400 text-sm mb-2">Failed to load component</p>
            <button
              onClick={() => this.setState({ hasError: false })}
              className="text-purple-400 hover:text-purple-300 text-xs underline"
            >
              Try again
            </button>
          </div>
        </div>
      )

    return this.props.children
  }
}

/**
 * Higher-order component for lazy loading with enhanced error handling
 */
export function withLazyLoading<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  options: {
    loadingMessage?: string
    fallback?: React.ReactNode
    errorFallback?: React.ReactNode
  } = {}
) {
  const LazyComponent = lazy(importFunc)

  return React.forwardRef<any, React.ComponentProps<T>>((props, ref) => (
    <LazyLoadErrorBoundary fallback={options.errorFallback}>
      <Suspense fallback={options.fallback || <LoadingSpinner message={options.loadingMessage} />}>
        <LazyComponent {...(props as any)} ref={ref} />
      </Suspense>
    </LazyLoadErrorBoundary>
  ))
}

/**
 * Lazy load admin components (heavy components that are rarely used)
 */
export const LazyAdminAnalytics = withLazyLoading(
  () => import('@/admin/components/analytics/AdminAnalytics'),
  { loadingMessage: 'Loading analytics...' }
)

/**
 * Intersection Observer hook for lazy loading on scroll
 */
export function useIntersectionObserver(
  ref: React.RefObject<Element>,
  options: IntersectionObserverInit = {})
) {
  const [isIntersecting, setIsIntersecting] = React.useState(false)
  const [hasIntersected, setHasIntersected] = React.useState(false)

  React.useEffect(() => {
    const element = ref.current
    if (!element) return

    const observer = new IntersectionObserver()
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [ref, hasIntersected, options])

  return { isIntersecting, hasIntersected }
}

/**
 * Lazy image component with intersection observer
 */
export const LazyImage: React.FC<{
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
  placeholder?: string
}> = ({ src, alt, className, width, height, placeholder }) => {
  const ref = React.useRef<HTMLDivElement>(null)
  const { hasIntersected } = useIntersectionObserver(ref)
  const [loaded, setLoaded] = React.useState(false)

  return (
    <div ref={ref} className={`relative overflow-hidden ${className}`}>
      {hasIntersected ? (
        <>
          {!loaded && placeholder && ()
            <div className="absolute inset-0 bg-gray-800 animate-pulse" />
          )}
          <img
            src={src}
            alt={alt}
            width={width}
            height={height}
            className={`transition-opacity duration-300 ${loaded ? 'opacity-100' : 'opacity-0'}`}
            onLoad={() => setLoaded(true)}
            loading="lazy"
          />
        </>
      ) : (
        <div className="w-full h-full bg-gray-800 animate-pulse" />
      )}
    </div>
  )
}

/**
 * Preload critical resources
 */
export function preloadCriticalResources() {
  // Preload critical CSS
  const criticalCSS = document.createElement('link')
  criticalCSS.rel = 'preload'
  criticalCSS.as = 'style'
  criticalCSS.href = '/critical.css'
  document.head.appendChild(criticalCSS)

  // Preload critical fonts
  const interFont = document.createElement('link')
  interFont.rel = 'preload'
  interFont.as = 'font'
  interFont.type = 'font/woff2'
  interFont.href = 'https://rsms.me/inter/font-files/Inter-Regular.woff2'
  interFont.crossOrigin = 'anonymous'
  document.head.appendChild(interFont)
}

/**
 * Dynamic import with retry logic
 */
export async function dynamicImportWithRetry<T>(
  importFunc: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000
): Promise<T> {
  try {
    return await importFunc()
  } catch (error) {
    if (retries > 0) {
      await new Promise(resolve => setTimeout(resolve, delay))
      return dynamicImportWithRetry(importFunc, retries - 1, delay * 2)
    }
    throw error
  }
}
