/**
 * Firestore database operations and type definitions
 * Provides comprehensive CRUD operations for all application entities
 * Includes error handling and optimized queries for performance
 */

import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  setDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  increment,
  writeBatch,
  onSnapshot,
  DocumentData,
  QueryDocumentSnapshot,
  Timestamp
} from 'firebase/firestore'
import { db } from './firebase'

import { logger, logDebug, logError, logTesting } from './utils/logger.ts'
/**
 * Type definitions for Firestore documents
 * All interfaces include proper field documentation and validation
 */
/**
 * Product variant interface
 * Represents individual variants of a product (e.g., different colors, sizes)
 */
export interface ProductVariant {
  /** Unique variant identifier */
  id: string
  /** Variant display name */
  name: string
  /** Variant description */
  description?: string
  /** Variant price in USD */
  price: number
  /** Variant image URL */
  image: string
  /** Available stock for this variant */
  stock: number
}

/**
 * Product interface for Firestore documents
 * Represents artisan keycap products in the database
 */
export interface Product {
  /** Unique document identifier */
  id: string
  /** Product display name */
  name: string
  /** Detailed product description */
  description: string
  /** Price in USD */
  price: number
  /** Primary product image URL */
  image: string
  /** Product category classification */
  category: string
  /** Whether this product is part of a raffle */
  isRaffle: boolean
  /** Raffle end date (only for raffle products) */
  raffleEndDate?: Timestamp
  /** Whether the product is currently sold out */
  soldOut: boolean
  /** Available stock quantity */
  stock: number
  /** Whether this product is featured on homepage */
  featured: boolean
  /** Product variants (for raffle products) */
  variants?: ProductVariant[]
  /** Point cost for reward shop (optional */
  pointsCost?: number
  /** Whether this product is only available for points */
  pointsOnly?: boolean
  /** Product tags for filtering and categorization */
  tags?: string[]
  /** Document creation timestamp */
  createdAt: Timestamp
  /** Last update timestamp */
  updatedAt: Timestamp
}

/**
 * Order interface for customer purchases
 * Tracks order lifecycle from creation to delivery
 */
/**
 * Enhanced User Profile interface for Phase 1 Community Rules Implementation
 * Extends existing UserProfile with comprehensive gamification, moderation, and community features
 */
export interface UserProfile {
  /** Unique user identifier */
  id: string
  /** User email address */
  email: string
  /** Display name */
  displayName?: string
  /** Profile photo URL */
  photoURL?: string
  /** User role in the system */
  role: 'user' | 'moderator' | 'admin' | 'superadmin'
  
  /** Enhanced gamification profile */
  gamification: {
    totalPoints: number
    currentTier: 'bronze' | 'silver' | 'gold' | 'platinum'
    tierProgress: number
    lastTierUpdate: Timestamp
    qualityScore: number
    achievementCount: number
    streakDays: number
    lastActivity: Timestamp
    joinDate: Timestamp
    membershipDays: number
    pointsEarnedToday: number
    pointsEarnedThisWeek: number
    pointsEarnedThisMonth: number
  }
  
  /** Moderation profile */
  moderation: {
    warningCount: number
    suspensionHistory: any[]
    lastViolation: Timestamp | null
    reputationScore: number
    reportCount: number
    helpfulContributions: number
    isSuspended: boolean
    suspensionEnds: Timestamp | null
    suspensionReason: string | null
    appealCount: number
    lastAppeal: Timestamp | null
  }
  
  /** User preferences for community features */
  preferences: {
    emailNotifications: boolean
    communityNotifications: boolean
    moderationAlerts: boolean
    privacyLevel: 'public' | 'private' | 'friends'
    languageFilter: boolean
    contentMaturityLevel: 'all' | 'teen' | 'mature'
    autoSaveContent: boolean
    displayAchievements: boolean
    displayPoints: boolean
    displayTier: boolean
  }
  
  /** Activity tracking */
  activity: {
    contentCreated: number
    commentsPosted: number
    likesGiven: number
    likesReceived: number
    sharesGiven: number
    sharesReceived: number
    reportsSubmitted: number
    moderationActionsReceived: number
    helpfulVotesReceived: number
    challengesParticipated: number
    challengesWon: number
  }
  
  /** User status and timestamps */
  isActive: boolean
  lastLoginAt?: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface Order {
  /** Unique order identifier */
  id: string
  /** Customer user ID who placed the order */
  userId: string
  /** Array of ordered items with quantities */
  items: OrderItem[]
  /** Total order amount in USD */
  totalAmount: number
  /** Current order processing status */
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
  /** Delivery address information */
  shippingAddress: ShippingAddress
  /** Payment method used (e.g., 'stripe', 'paypal') */
  paymentMethod: string
  /** Current payment processing status */
  paymentStatus: 'pending' | 'paid' | 'failed'
  /** Order creation timestamp */
  createdAt: Timestamp
  /** Last update timestamp */
  updatedAt: Timestamp
}

export interface OrderItem {
  productId: string
  productName: string
  price: number
  quantity: number
}

export interface ShippingAddress {
  id?: string
  userId: string
  name: string
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  phone: string
  isDefault: boolean
}

export interface RaffleEntry {
  id: string
  raffleId: string
  userId: string
  userEmail: string
  userName: string
  productId?: string // For backward compatibility
  productIds?: string[] // For multiple product selection
  variantId?: string
  discordUsername?: string
  shippingAddress: {
    fullName: string
    address: string
    city: string
    state: string
    zipCode: string
    country: string
    phone: string
  }
  shippingMethod?: string
  answer?: string
  status: 'pending' | 'confirmed' | 'winner' | 'loser'
  entryDate: Timestamp
  createdAt: Timestamp
}

export interface Review {
  id: string
  userId: string
  productId: string
  rating: number
  comment: string
  status: 'pending' | 'approved' | 'rejected'
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  featuredImage?: string
  category: string
  tags: string[]
  published: boolean
  featured: boolean
  author: string
  views: number
  likes: number
  seoTitle?: string
  seoDescription?: string
  seoKeywords?: string[]
  scheduledAt?: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface BlogCategory {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  postCount: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface BlogTag {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  postCount: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface BlogComment {
  id: string
  postId: string
  parentId?: string // For nested replies
  authorId?: string // User ID if logged in
  authorName: string
  authorEmail: string
  authorAvatar?: string
  content: string
  approved: boolean
  flagged: boolean
  flagCount: number
  likes: number
  replies: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface UserProfile {
  id: string
  email: string
  displayName?: string
  photoURL?: string
  role: 'user' | 'admin' | 'superadmin'
  points: number
  isActive: boolean
  lastLoginAt?: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface PointTransaction {
  id: string
  userId: string
  type: 'earned' | 'redeemed'
  source: 'purchase' | 'review' | 'raffle_win' | 'signup_bonus' | 'reward' | 'referral'
  points: number
  description: string
  orderId?: string
  productId?: string
  balance: number
  createdAt: Timestamp
}

export interface Reward {
  id: string
  name: string
  description: string
  pointsCost: number
  type: 'discount' | 'shipping' | 'product'
  value: number
  isActive: boolean
  stock: number // -1 for unlimited
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface UserPreferences {
  userId: string
  emailNotifications: boolean
  smsNotifications: boolean
  raffleNotifications: boolean
  orderUpdates: boolean
  marketingEmails: boolean
  newsletter: boolean
  language: string
  timezone: string
  currency: string
  updatedAt: Timestamp
}

export interface Notification {
  id: string
  userId: string
  type: 'order_update' | 'raffle_result' | 'points_earned' | 'system'
  title: string
  message: string
  data?: Record<string, any>
  read: boolean
  createdAt: Timestamp
  readAt?: Timestamp
}

export interface Coupon {
  id: string
  code: string
  type: 'percentage' | 'fixed' | 'shipping'
  value: number
  minOrderAmount: number
  maxUses: number // -1 for unlimited
  usedCount: number
  isActive: boolean
  expiresAt: Timestamp
  createdAt: Timestamp
}

export interface UserCoupon {
  id: string
  userId: string
  couponId: string
  orderId?: string
  usedAt: Timestamp
  createdAt: Timestamp
}

export interface SecurityLog {
  id: string
  userId: string
  action: 'login' | 'logout' | 'password-change' | 'email-change' | 'profile-update' | 'address-change'
  ipAddress: string
  userAgent: string
  location?: string
  success: boolean
  details?: Record<string, any>
  createdAt: Timestamp
}

export interface WishlistItem {
  id: string
  userId: string
  productId: string
  addedAt: Timestamp
  notifyOnRestock: boolean
}

export interface ProductView {
  id: string
  productId: string
  userId?: string
  sessionId: string
  viewedAt: Timestamp
  duration?: number
}

// ===== PHASE 1 ENHANCEMENT INTERFACES =====

export interface GamificationRule {
  id: string
  type: 'points' | 'achievement' | 'reward' | 'notification'
  name: string
  description: string
  conditions: RuleCondition[]
  actions: RuleAction[]
  schedule?: RuleSchedule
  isActive: boolean
  priority: number
  analytics: RuleAnalytics
  createdBy: string
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface RuleCondition {
  type: 'user_tier' | 'purchase_amount' | 'time_range' | 'user_segment' | 'custom'
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'in_range'
  value: any
  metadata?: Record<string, any>
}

export interface RuleAction {
  type: 'award_points' | 'multiply_points' | 'award_achievement' | 'send_notification'
  value: number | string
  metadata?: Record<string, any>
}

export interface RuleSchedule {
  startDate?: Timestamp
  endDate?: Timestamp
  timeZone: string
  recurring?: {
    frequency: 'daily' | 'weekly' | 'monthly'
    interval: number
    daysOfWeek?: number[]
  }
}

export interface RuleAnalytics {
  executionCount: number
  lastExecuted?: Timestamp
  averageExecutionTime: number
  errorCount: number
  successRate: number
  impactMetrics?: {
    usersAffected: number
    pointsAwarded: number
    achievementsUnlocked: number
  }
}

export interface UserSegment {
  id: string
  name: string
  description: string
  criteria: SegmentCriteria[]
  userCount: number
  lastCalculated: Timestamp
  isActive: boolean
  isDynamic: boolean
  refreshInterval?: number // minutes
  createdBy: string
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface SegmentCriteria {
  field: 'tier' | 'points' | 'last_activity' | 'registration_date' | 'purchase_history' | 'achievement_count'
  operator: 'equals' | 'greater_than' | 'less_than' | 'in_range' | 'contains'
  value: any
  weight?: number // for scoring-based segments
}

export interface BulkOperation {
  id: string
  type: 'points' | 'achievement' | 'notification' | 'tier' | 'segment_update'
  operation: {
    action: string
    value: any
    metadata?: Record<string, any>
  }
  targetSegment: string
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: {
    total: number
    processed: number
    successful: number
    failed: number
  }
  results?: {
    successfulUsers: string[]
    failedUsers: Array<{
      userId: string
      error: string
    }>
    summary: Record<string, any>
  }
  scheduledFor?: Timestamp
  startedAt?: Timestamp
  completedAt?: Timestamp
  estimatedDuration?: number // seconds
  createdBy: string
  createdAt: Timestamp
}

export interface SystemAlert {
  id: string
  type: 'error' | 'warning' | 'info' | 'success'
  category: 'performance' | 'security' | 'data_integrity' | 'user_activity' | 'system_health'
  title: string
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  isRead: boolean
  isResolved: boolean
  metadata?: Record<string, any>
  triggeredBy?: string
  resolvedBy?: string
  resolvedAt?: Timestamp
  createdAt: Timestamp
}

export interface AdminAuditLog {
  id: string
  adminId: string
  adminEmail: string
  action: string
  resource: string
  resourceId?: string
  changes?: {
    before: Record<string, any>
    after: Record<string, any>
  }
  ipAddress: string
  userAgent: string
  sessionId: string
  success: boolean
  errorMessage?: string
  metadata?: Record<string, any>
  createdAt: Timestamp
}

// Collection references
export const collections = {
  products: 'products',
  orders: 'orders',
  shippingAddresses: 'shipping_addresses',
  raffleEntries: 'raffle_entries',
  raffles: 'raffles',
  reviews: 'reviews',
  profiles: 'profiles',
  blogPosts: 'blog_posts',
  blogCategories: 'blog_categories',
  blogTags: 'blog_tags',
  blogComments: 'blog_comments',
  pointTransactions: 'pointTransactions',
  
  // Phase 1 Community Collections
  communityContent: 'communityContent',
  moderationQueue: 'moderationQueue',
  moderationActions: 'moderationActions',
  moderationFlags: 'moderationFlags',
  appeals: 'appeals',
  communityRules: 'communityRules',
  communityNotifications: 'communityNotifications',
  communityAnalytics: 'communityAnalytics',
  dailyActivityLimits: 'dailyActivityLimits',
  userTiers: 'userTiers',
  tierPromotions: 'tierPromotions',
  rewards: 'rewards',
  userPreferences: 'userPreferences',
  notifications: 'notifications',
  coupons: 'coupons',
  userCoupons: 'userCoupons',
  securityLogs: 'securityLogs',
  wishlist: 'wishlist',
  productViews: 'productViews',
  // Enhanced Gamification Collections
  achievements: 'achievements',
  userAchievements: 'user_achievements',
  pointHistory: 'point_history',
  rewardPurchases: 'reward_purchases',
  userActivities: 'user_activities',
  leaderboards: 'leaderboards',
  badges: 'badges',
  userBadges: 'user_badges',
  gamificationSettings: 'gamification_settings',
  achievementProgress: 'achievement_progress',
  // Phase 1 Enhancement Collections
  gamificationRules: 'gamification_rules',
  userSegments: 'user_segments',
  bulkOperations: 'bulk_operations',
  systemAlerts: 'system_alerts',
  adminAuditLog: 'admin_audit_log',
  // Community and Collaboration Collections
  submissions: 'submissions',
  challenges: 'challenges',
  challengeParticipations: 'challenge_participations',
  discussions: 'discussions',
  discussionReplies: 'discussion_replies',
  messages: 'messages',
  conversations: 'conversations',
  workspaces: 'workspaces',
  workspaceDocuments: 'workspace_documents',
  workspaceTasks: 'workspace_tasks',
  contentTemplates: 'content_templates',
  richContent: 'rich_content',
  contentBuilders: 'content_builders',
  socialGroups: 'social_groups',
  groupPosts: 'group_posts',
  groupComments: 'group_comments',
  mentorProfiles: 'mentor_profiles',
  menteeProfiles: 'mentee_profiles',
  mentorshipMatches: 'mentorship_matches',
  learningPaths: 'learning_paths',
  learningSessions: 'learning_sessions',
  knowledgeBase: 'knowledge_base',
  notificationTemplates: 'notification_templates',
  notificationDigests: 'notification_digests',
  notificationAnalytics: 'notification_analytics',
  // Tier Management Collections
  tiers: 'tiers',
  tierPromotions: 'tier_promotions'
} as const

/**
 * Product Management Functions
 * Handles all product-related database operations
 */

/**
 * Retrieves products from Firestore with optional filtering
 * Implements smart query optimization to handle index limitations
 *
 * @param options - Optional filtering and pagination parameters
 * @param options.category - Filter by product category
 * @param options.featured - Filter by featured status
 * @param options.isRaffle - Filter by raffle status
 * @param options.limitCount - Maximum number of products to return
 * @returns Promise<Product[]> - Array of products matching criteria
 */
export const getProducts = async (options?: {
  category?: string
  featured?: boolean
  isRaffle?: boolean
  limitCount?: number
}) => {
  try {
    let q = query(collection(db, collections.products))

    // Handle single filter queries first (these work without composite index)
    if (options?.category && !options?.featured && !options?.isRaffle) {
      q = query(q, where('category', '==', options.category), orderBy('createdAt', 'desc'))
    } else if (options?.featured !== undefined && !options?.category && !options?.isRaffle) {
      q = query(q, where('featured', '==', options.featured), orderBy('createdAt', 'desc'))
    } else if (options?.isRaffle !== undefined && !options?.category && !options?.featured) {
      q = query(q, where('isRaffle', '==', options.isRaffle), orderBy('createdAt', 'desc'))
    } else if (options?.category || options?.featured !== undefined || options?.isRaffle !== undefined) {
      // For multiple filters, get all products and filter in memory (temporary solution)
      q = query(collection(db, collections.products))
      const snapshot = await getDocs(q)
      let products = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Product))

      // Apply filters in memory
      if (options?.category) {
        products = products.filter(p => p.category === options.category)
      }
      if (options?.featured !== undefined) {
        products = products.filter(p => p.featured === options.featured)
      }
      if (options?.isRaffle !== undefined) {
        products = products.filter(p => p.isRaffle === options.isRaffle)
      }

      // Sort by createdAt in memory
      products.sort((a, b) => b.createdAt?.toDate().getTime() - a.createdAt?.toDate().getTime())

      // Apply limit
      if (options?.limitCount) {
        products = products.slice(0, options.limitCount)
      }

      return products
    } else {
      // No filters, just order by createdAt
      q = query(q, orderBy('createdAt', 'desc'))
    }

    if (options?.limitCount) {
      q = query(q, limit(options.limitCount))
    }

    const snapshot = await getDocs(q)
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Product))
  } catch (error) {
    console.error('Error in getProducts:', error)
    // Fallback: get all products and filter in memory
    const snapshot = await getDocs(collection(db, collections.products))
    let products = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Product))

    // Apply filters in memory
    if (options?.category) {
      products = products.filter(p => p.category === options.category)
    }
    if (options?.featured !== undefined) {
      products = products.filter(p => p.featured === options.featured)
    }
    if (options?.isRaffle !== undefined) {
      products = products.filter(p => p.isRaffle === options.isRaffle)
    }

    // Sort by createdAt in memory
    products.sort((a, b) => b.createdAt?.toDate().getTime() - a.createdAt?.toDate().getTime())

    // Apply limit
    if (options?.limitCount) {
      products = products.slice(0, options.limitCount)
    }

    return products
  }
}

/**
 * Retrieves a single product by ID
 *
 * @param id - Product document ID
 * @returns Promise<Product | null> - Product data or null if not found
 */
export const getProduct = async (id: string): Promise<Product | null> => {
  const docRef = doc(db, collections.products, id)
  const docSnap = await getDoc(docRef)

  if (docSnap.exists()) {
    return { id: docSnap.id, ...docSnap.data() } as Product
  }
  return null
}

export const createProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
  const docRef = await addDoc(collection(db, collections.products), {
    ...productData,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  })
  return docRef.id
}

export const updateProduct = async (id: string, updates: Partial<Product>) => {
  const docRef = doc(db, collections.products, id)
  await updateDoc(docRef, {
    ...updates,
    updatedAt: serverTimestamp()
  })
}

export const deleteProduct = async (id: string) => {
  const docRef = doc(db, collections.products, id)
  await deleteDoc(docRef)
}

// Orders
export const getUserOrders = async (userId: string) => {
  const q = query(
    collection(db, collections.orders),
    where('userId', '==', userId),
    orderBy('createdAt', 'desc')
  )
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Order))
}

export const createOrder = async (orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => {
  const docRef = await addDoc(collection(db, collections.orders), {
    ...orderData,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  })
  return docRef.id
}

export const updateOrderStatus = async (orderId: string, status: Order['status']) => {
  const docRef = doc(db, collections.orders, orderId)
  await updateDoc(docRef, {
    status,
    updatedAt: serverTimestamp()
  })
}

// Shipping Addresses
export const getUserShippingAddresses = async (userId: string) => {
  const q = query(
    collection(db, collections.shippingAddresses),
    where('userId', '==', userId),
    orderBy('isDefault', 'desc'),
    orderBy('name', 'asc')
  )
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as ShippingAddress))
}

export const createShippingAddress = async (addressData: Omit<ShippingAddress, 'id'>) => {
  const docRef = await addDoc(collection(db, collections.shippingAddresses), addressData)
  return docRef.id
}

export const updateShippingAddress = async (addressId: string, updates: Partial<ShippingAddress>) => {
  const addressRef = doc(db, collections.shippingAddresses, addressId)
  await updateDoc(addressRef, updates)
}

export const deleteShippingAddress = async (addressId: string) => {
  const addressRef = doc(db, collections.shippingAddresses, addressId)
  await deleteDoc(addressRef)
}

export const setDefaultShippingAddress = async (userId: string, addressId: string) => {
  // First, remove default status from all user addresses
  const userAddresses = await getUserShippingAddresses(userId)
  const updatePromises = userAddresses.map(address =>
    updateShippingAddress(address.id!, { isDefault: false })
  )
  await Promise.all(updatePromises)

  // Then set the selected address as default
  await updateShippingAddress(addressId, { isDefault: true })
}

// Raffle Entries

/**
 * Check if user has already entered a raffle for specific products
 * Prevents duplicate entries for the same products
 *
 * @param userId - User ID to check
 * @param productIds - Array of product IDs to check
 * @returns Promise<boolean> - true if duplicate entry exists
 */
export const checkDuplicateRaffleEntry = async (userId: string, productIds: string[]): Promise<boolean> => {
  try {
    const q = query(
      collection(db, collections.raffleEntries),
      where('userId', '==', userId)
    )

    const snapshot = await getDocs(q)

    // Check if user has already entered for any of the same products
    for (const doc of snapshot.docs) {
      const entryData = doc.data() as RaffleEntry
      const existingProductIds = entryData.productIds || []

      // Check for any overlap in product IDs
      const hasOverlap = productIds.some(productId =>
        existingProductIds.includes(productId)
      )

      if (hasOverlap) {
        logger.info('general', '🚫 Duplicate raffle entry detected for products:', productIds)
        logger.info('general', 'Existing entry:', doc.id)
        return true
      }
    }

    return false
  } catch (error) {
    console.error('Error checking duplicate raffle entry:', error)
    // In case of error, allow the entry (fail open)
    return false
  }
}

/**
 * Enhanced duplicate entry checking with detailed error information
 */
export const checkRaffleEntryEligibility = async (
  userId: string,
  raffleId: string
): Promise<{ eligible: boolean; reason?: string; existingEntry?: any }> => {
  try {
    const existingEntryQuery = query(
      collection(db, collections.raffleEntries),
      where('userId', '==', userId),
      where('raffleId', '==', raffleId)
    )

    const existingEntries = await getDocs(existingEntryQuery)

    if (!existingEntries.empty) {
      const existingEntry = existingEntries.docs[0].data()
      return {
        eligible: false,
        reason: 'You have already entered this raffle. Each user can only enter once per raffle.',
        existingEntry: {
          id: existingEntries.docs[0].id,
          entryDate: existingEntry.entryDate,
          status: existingEntry.status
        }
      }
    }

    return { eligible: true }
  } catch (error) {
    console.error('Error checking raffle entry eligibility:', error)
    // In case of error, allow the entry (fail open) but log the issue
    return {
      eligible: true,
      reason: 'Unable to verify entry eligibility due to system error. Proceeding with entry.'
    }
  }
}

export const createRaffleEntry = async (entryData: Omit<RaffleEntry, 'id' | 'createdAt'>) => {
  // Enhanced duplicate entry checking
  const eligibilityCheck = await checkRaffleEntryEligibility(entryData.userId, entryData.raffleId)

  if (!eligibilityCheck.eligible) {
    const error = new Error(eligibilityCheck.reason || 'Entry not allowed')
    // Add additional context for better error handling
    ;(error as any).code = 'DUPLICATE_ENTRY'
    ;(error as any).existingEntry = eligibilityCheck.existingEntry
    throw error
  }

  const docRef = await addDoc(collection(db, collections.raffleEntries), {
    ...entryData,
    createdAt: serverTimestamp()
  })

  // Track raffle entry activity
  try {
    const { logActivity } = await import('./activitySystem')

    // Prepare metadata without undefined values
    const metadata: Record<string, any> = {
      raffleId: entryData.raffleId,
      productIds: entryData.productIds || [],
      selectedVariants: (entryData as any).selectedVariants || []
    }

    // Only add social choices if they exist and have values
    const socialChoices = (entryData as any).socialChoices
    if (socialChoices) {
      if (socialChoices.instagramPostUrl) {
        metadata.instagramPostUrl = socialChoices.instagramPostUrl
      }
      if (socialChoices.redditUsername) {
        metadata.redditUsername = socialChoices.redditUsername
      }
      metadata.socialRequirements = {
        instagramFollowed: socialChoices.instagramFollowed || false,
        discordJoined: socialChoices.discordJoined || false,
        redditFollowed: socialChoices.redditFollowed || false
      }
    }

    await logActivity(
      entryData.userId,
      'raffle_entry',
      'Raffle Entry Submitted',
      `Entered raffle: ${entryData.raffleId}`,
      '127.0.0.1', // Will be updated with actual IP in production
      'Unknown', // Will be updated with actual location
      'Web Browser', // Will be updated with actual device info
      true,
      metadata
    )
  } catch (error) {
    console.error('Failed to track raffle entry activity:', error)
    // Don't throw error here - raffle entry should still succeed even if activity tracking fails
  }

  return docRef.id
}

export const getUserRaffleEntries = async (userId: string) => {
  const q = query(
    collection(db, collections.raffleEntries),
    where('userId', '==', userId),
    orderBy('createdAt', 'desc')
  )
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as RaffleEntry))
}

// Reviews
export const getProductReviews = async (productId: string) => {
  const q = query(
    collection(db, collections.reviews),
    where('productId', '==', productId),
    where('status', '==', 'approved'),
    orderBy('createdAt', 'desc')
  )
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Review))
}

export const getFeaturedReviews = async (limitCount: number = 6) => {
  const q = query(
    collection(db, collections.reviews),
    where('status', '==', 'approved'),
    orderBy('createdAt', 'desc'),
    limit(limitCount)
  )
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Review))
}

export const createReview = async (reviewData: Omit<Review, 'id' | 'createdAt' | 'updatedAt'>) => {
  const docRef = await addDoc(collection(db, collections.reviews), {
    ...reviewData,
    status: 'pending',
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  })

  // Award points for review and check achievements
  try {
    const { PointsSystem } = await import('./pointsSystem')
    const { AchievementSystem } = await import('./achievementSystem')
    const { getUserProfile } = await import('./auth')

    // Award review points
    const hasMedia = false // PLANNED: Add images support to Review type
    await PointsSystem.awardReviewPoints(reviewData.userId, reviewData.productId, hasMedia)

    // Check for review-related achievements
    const userProfile = await getUserProfile(reviewData.userId)
    if (userProfile) {
      await AchievementSystem.checkAchievements(reviewData.userId, userProfile, {
        type: 'review',
        data: {
          productId: reviewData.productId,
          rating: reviewData.rating,
          hasMedia
        }
      })
    }
  } catch (error) {
    console.error('Failed to award points or check achievements for review:', error)
    // Don't fail the review creation if points/achievements fail
  }

  return docRef.id
}

// Admin functions
export const getAllOrders = async () => {
  const q = query(collection(db, collections.orders), orderBy('createdAt', 'desc'))
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Order))
}

export const getPendingReviews = async () => {
  const q = query(
    collection(db, collections.reviews),
    where('status', '==', 'pending'),
    orderBy('createdAt', 'desc')
  )
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Review))
}

export const updateReviewStatus = async (reviewId: string, status: Review['status']) => {
  const docRef = doc(db, collections.reviews, reviewId)
  await updateDoc(docRef, {
    status,
    updatedAt: serverTimestamp()
  })
}

// Note: Admin statistics have been moved to @/admin/lib/adminFirestore
// Use getAdminStats() from the centralized admin module instead

// Blog Posts

/**
 * Retrieves blog posts from Firestore with optional filtering
 *
 * @param {Object} options - Optional filtering and pagination options
 * @param {boolean} options.published - Filter by published status
 * @param {boolean} options.featured - Filter by featured status
 * @param {string} options.category - Filter by category
 * @param {number} options.limitCount - Maximum number of posts to return
 * @returns {Promise<BlogPost[]>} Array of blog posts matching the criteria
 * <AUTHOR> Team
 */
export const getBlogPosts = async (options?: {
  published?: boolean
  featured?: boolean
  category?: string
  limitCount?: number
}) => {
  let q = query(collection(db, collections.blogPosts))

  if (options?.published !== undefined) {
    q = query(q, where('published', '==', options.published))
  }
  if (options?.featured !== undefined) {
    q = query(q, where('featured', '==', options.featured))
  }
  if (options?.category) {
    q = query(q, where('category', '==', options.category))
  }

  q = query(q, orderBy('createdAt', 'desc'))

  if (options?.limitCount) {
    q = query(q, limit(options.limitCount))
  }

  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as BlogPost))
}

/**
 * Retrieves a single blog post by its slug
 *
 * @param {string} slug - The URL slug of the blog post
 * @returns {Promise<BlogPost | null>} The blog post if found, null otherwise
 * <AUTHOR> Team
 */
export const getBlogPost = async (slug: string): Promise<BlogPost | null> => {
  const q = query(collection(db, collections.blogPosts), where('slug', '==', slug))
  const snapshot = await getDocs(q)

  if (!snapshot.empty) {
    const doc = snapshot.docs[0]
    return { id: doc.id, ...doc.data() } as BlogPost
  }
  return null
}

/**
 * Creates a new blog post in Firestore
 *
 * @param {Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt' | 'views' | 'likes'>} postData - Blog post data
 * @returns {Promise<string>} The ID of the created blog post
 * <AUTHOR> Team
 */
export const createBlogPost = async (postData: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt' | 'views' | 'likes'>) => {
  const docRef = await addDoc(collection(db, collections.blogPosts), {
    ...postData,
    views: 0,
    likes: 0,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  })
  return docRef.id
}

export const updateBlogPost = async (id: string, updates: Partial<BlogPost>) => {
  const docRef = doc(db, collections.blogPosts, id)
  await updateDoc(docRef, {
    ...updates,
    updatedAt: serverTimestamp()
  })
}

export const incrementBlogPostViews = async (id: string) => {
  const docRef = doc(db, collections.blogPosts, id)
  await updateDoc(docRef, {
    views: increment(1)
  })
}

export const deleteBlogPost = async (id: string) => {
  const docRef = doc(db, collections.blogPosts, id)
  await deleteDoc(docRef)
}

// Blog Categories
export const getBlogCategories = async () => {
  const q = query(collection(db, collections.blogCategories), orderBy('name', 'asc'))
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as BlogCategory))
}

export const getBlogCategory = async (slug: string): Promise<BlogCategory | null> => {
  const q = query(collection(db, collections.blogCategories), where('slug', '==', slug))
  const snapshot = await getDocs(q)

  if (!snapshot.empty) {
    const doc = snapshot.docs[0]
    return { id: doc.id, ...doc.data() } as BlogCategory
  }
  return null
}

export const createBlogCategory = async (categoryData: Omit<BlogCategory, 'id' | 'createdAt' | 'updatedAt' | 'postCount'>) => {
  const docRef = await addDoc(collection(db, collections.blogCategories), {
    ...categoryData,
    postCount: 0,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  })
  return docRef.id
}

export const updateBlogCategory = async (id: string, updates: Partial<BlogCategory>) => {
  const docRef = doc(db, collections.blogCategories, id)
  await updateDoc(docRef, {
    ...updates,
    updatedAt: serverTimestamp()
  })
}

export const deleteBlogCategory = async (id: string) => {
  const docRef = doc(db, collections.blogCategories, id)
  await deleteDoc(docRef)
}

// Blog Tags
export const getBlogTags = async () => {
  const q = query(collection(db, collections.blogTags), orderBy('name', 'asc'))
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as BlogTag))
}

export const getBlogTag = async (slug: string): Promise<BlogTag | null> => {
  const q = query(collection(db, collections.blogTags), where('slug', '==', slug))
  const snapshot = await getDocs(q)

  if (!snapshot.empty) {
    const doc = snapshot.docs[0]
    return { id: doc.id, ...doc.data() } as BlogTag
  }
  return null
}

export const createBlogTag = async (tagData: Omit<BlogTag, 'id' | 'createdAt' | 'updatedAt' | 'postCount'>) => {
  const docRef = await addDoc(collection(db, collections.blogTags), {
    ...tagData,
    postCount: 0,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  })
  return docRef.id
}

export const updateBlogTag = async (id: string, updates: Partial<BlogTag>) => {
  const docRef = doc(db, collections.blogTags, id)
  await updateDoc(docRef, {
    ...updates,
    updatedAt: serverTimestamp()
  })
}

export const deleteBlogTag = async (id: string) => {
  const docRef = doc(db, collections.blogTags, id)
  await deleteDoc(docRef)
}

/**
 * Get related blog posts based on category, tags, and content similarity
 * @param {string} currentPostId - ID of the current post to exclude from results
 * @param {string} category - Category of the current post
 * @param {string[]} tags - Tags of the current post
 * @param {number} limit - Maximum number of related posts to return
 * @returns {Promise<BlogPost[]>} Array of related blog posts
 */
export const getRelatedBlogPosts = async (
  currentPostId: string,
  category: string,
  tags: string[],
  limitCount: number = 3
): Promise<BlogPost[]> => {
  try {
    // First, try to get posts from the same category
    let relatedPosts: BlogPost[] = []

    if (category) {
      const categoryQuery = query(
        collection(db, collections.blogPosts),
        where('published', '==', true),
        where('category', '==', category),
        orderBy('createdAt', 'desc'),
        limit(limitCount + 1) // Get one extra to account for current post
      )

      const categorySnapshot = await getDocs(categoryQuery)
      relatedPosts = categorySnapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() } as BlogPost))
        .filter(post => post.id !== currentPostId)
        .slice(0, limitCount)
    }

    // If we don't have enough posts from the same category, get posts with similar tags
    if (relatedPosts.length < limitCount && tags.length > 0) {
      const remainingLimit = limitCount - relatedPosts.length
      const existingIds = relatedPosts.map(post => post.id)

      const tagQuery = query(
        collection(db, collections.blogPosts),
        where('published', '==', true),
        where('tags', 'array-contains-any', tags),
        orderBy('createdAt', 'desc'),
        limit(remainingLimit + 5) // Get extra to filter out existing posts
      )

      const tagSnapshot = await getDocs(tagQuery)
      const tagPosts = tagSnapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() } as BlogPost))
        .filter(post => post.id !== currentPostId && !existingIds.includes(post.id))
        .slice(0, remainingLimit)

      relatedPosts = [...relatedPosts, ...tagPosts]
    }

    // If we still don't have enough posts, get the latest published posts
    if (relatedPosts.length < limitCount) {
      const remainingLimit = limitCount - relatedPosts.length
      const existingIds = relatedPosts.map(post => post.id)

      const latestQuery = query(
        collection(db, collections.blogPosts),
        where('published', '==', true),
        orderBy('createdAt', 'desc'),
        limit(remainingLimit + 5)
      )

      const latestSnapshot = await getDocs(latestQuery)
      const latestPosts = latestSnapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() } as BlogPost))
        .filter(post => post.id !== currentPostId && !existingIds.includes(post.id))
        .slice(0, remainingLimit)

      relatedPosts = [...relatedPosts, ...latestPosts]
    }

    return relatedPosts.slice(0, limitCount)
  } catch (error) {
    console.error('Error fetching related blog posts:', error)
    return []
  }
}

// Blog Comments

/**
 * Get comments for a specific blog post
 * @param {string} postId - ID of the blog post
 * @param {boolean} approvedOnly - Whether to fetch only approved comments
 * @returns {Promise<BlogComment[]>} Array of blog comments
 */
export const getBlogComments = async (postId: string, approvedOnly: boolean = true): Promise<BlogComment[]> => {
  try {
    let q = query(
      collection(db, collections.blogComments),
      where('postId', '==', postId),
      orderBy('createdAt', 'desc')
    )

    if (approvedOnly) {
      q = query(
        collection(db, collections.blogComments),
        where('postId', '==', postId),
        where('approved', '==', true),
        orderBy('createdAt', 'desc')
      )
    }

    const snapshot = await getDocs(q)
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as BlogComment))
  } catch (error) {
    console.error('Error fetching blog comments:', error)
    return []
  }
}

/**
 * Get all comments for admin moderation
 * @param {Object} options - Query options
 * @returns {Promise<BlogComment[]>} Array of all blog comments
 */
export const getAllBlogComments = async (options?: {
  approved?: boolean
  flagged?: boolean
  limitCount?: number
}): Promise<BlogComment[]> => {
  try {
    let q = query(collection(db, collections.blogComments), orderBy('createdAt', 'desc'))

    if (options?.approved !== undefined) {
      q = query(q, where('approved', '==', options.approved))
    }

    if (options?.flagged !== undefined) {
      q = query(q, where('flagged', '==', options.flagged))
    }

    if (options?.limitCount) {
      q = query(q, limit(options.limitCount))
    }

    const snapshot = await getDocs(q)
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as BlogComment))
  } catch (error) {
    console.error('Error fetching all blog comments:', error)
    return []
  }
}

/**
 * Create a new blog comment
 * @param {Omit<BlogComment, 'id' | 'createdAt' | 'updatedAt' | 'approved' | 'flagged' | 'flagCount' | 'likes' | 'replies'>} commentData - Comment data
 * @returns {Promise<string>} The ID of the created comment
 */
export const createBlogComment = async (
  commentData: Omit<BlogComment, 'id' | 'createdAt' | 'updatedAt' | 'approved' | 'flagged' | 'flagCount' | 'likes' | 'replies'>
): Promise<string> => {
  const docRef = await addDoc(collection(db, collections.blogComments), {
    ...commentData,
    approved: false, // Comments require moderation by default
    flagged: false,
    flagCount: 0,
    likes: 0,
    replies: 0,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  })
  return docRef.id
}

/**
 * Update a blog comment
 * @param {string} id - Comment ID
 * @param {Partial<BlogComment>} updates - Updates to apply
 */
export const updateBlogComment = async (id: string, updates: Partial<BlogComment> => {
  const docRef = doc(db, collections.blogComments, id)
  await updateDoc(docRef, {
    ...updates,
    updatedAt: serverTimestamp()
  })
}

/**
 * Delete a blog comment
 * @param {string} id - Comment ID
 */
export const deleteBlogComment = async (id: string => {
  const docRef = doc(db, collections.blogComments, id)
  await deleteDoc(docRef)
}

/**
 * Approve a blog comment
 * @param {string} id - Comment ID
 */
export const approveBlogComment = async (id: string => {
  await updateBlogComment(id, { approved: true })
}

/**
 * Flag a blog comment
 * @param {string} id - Comment ID
 */
export const flagBlogComment = async (id: string => {
  const commentRef = doc(db, collections.blogComments, id)
  const commentDoc = await getDoc(commentRef)

  if (commentDoc.exists()) {
    const currentFlagCount = commentDoc.data().flagCount || 0
    await updateDoc(commentRef, {
      flagged: true,
      flagCount: currentFlagCount + 1)
      updatedAt: serverTimestamp()
    })
  }
}

/**
 * Like a blog comment
 * @param {string} id - Comment ID
 */
export const likeBlogComment = async (id: string => {
  const commentRef = doc(db, collections.blogComments, id)
  const commentDoc = await getDoc(commentRef)

  if (commentDoc.exists()) {
    const currentLikes = commentDoc.data().likes || 0
    await updateDoc(commentRef, {
      likes: currentLikes + 1)
      updatedAt: serverTimestamp()
    })
  }
}

// User Profiles
export const getUserProfiles = async () => {
  const snapshot = await getDocs(collection(db, collections.profiles))
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as UserProfile))
}

export const getUserProfile = async (userId: string: Promise<UserProfile | null> => {
  const docRef = doc(db, collections.profiles, userId)
  const docSnap = await getDoc(docRef)

  if (docSnap.exists()) {
    return { id: docSnap.id, ...docSnap.data() } as UserProfile
  }
  return null
}

export const createUserProfile = async (userId: string, profileData: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>) => {
  const docRef = doc(db, collections.profiles, userId)
  await setDoc(docRef, {
    ...profileData,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  })
}

export const updateUserProfile = async (userId: string, updates: Partial<UserProfile> => {
  const docRef = doc(db, collections.profiles, userId)
  await updateDoc(docRef, {
    ...updates,
    updatedAt: serverTimestamp()
  })
}

// Reviews
export const getReviews = async () => {
  const q = query(collection(db, collections.reviews), orderBy('createdAt', 'desc'))
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Review))
}

export const updateReview = async (reviewId: string, updates: Partial<Review> => {
  const docRef = doc(db, collections.reviews, reviewId)
  await updateDoc(docRef, {
    ...updates,
    updatedAt: serverTimestamp()
  })
}

// Raffle Entries
export const getRaffleEntries = async (raffleId?: string) => {
  let q
  if (raffleId) {
    q = query()
      collection(db, collections.raffleEntries),
      where('raffleId', '==', raffleId),
      orderBy('createdAt', 'desc')
    )
  } else {
    q = query(collection(db, collections.raffleEntries), orderBy('createdAt', 'desc'))
  }
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as RaffleEntry))
}

// Get raffle by product ID
export const getRaffleByProductId = async (productId: string => {
  const q = query()
    collection(db, collections.raffles),
    where('productId', '==', productId)
  )
  const snapshot = await getDocs(q)
  if (snapshot.empty) return null

  const raffleDoc = snapshot.docs[0]
  return { id: raffleDoc.id, ...raffleDoc.data() }
}

// Get active raffles
export const getActiveRaffles = async () => {
  const q = query()
    collection(db, collections.raffles),
    where('status', '==', 'active')
  )
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
}

// Orders Management
export const getOrders = async () => {
  const q = query(collection(db, collections.orders), orderBy('createdAt', 'desc'))
  const snapshot = await getDocs(q)
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Order))
}

export const updateOrder = async (orderId: string, updates: Partial<Order> => {
  const docRef = doc(db, collections.orders, orderId)
  await updateDoc(docRef, {
    ...updates,
    updatedAt: serverTimestamp()
  })
}

// Additional utility functions can be added here
