/**
 * SECURITY: HTML Sanitization Utility
 * 
 * Critical security utility for preventing XSS attacks through HTML injection.
 * Uses DOMPurify to safely clean user-generated content before rendering.
 * 
 * <AUTHOR> Security Team
 * @version 1.0.0
 * @security CRITICAL - Do not modify without security review
 */

import DOMPurify from 'dompurify';

export interface SanitizationConfig {
  allowedTags?: string[];
  allowedAttributes?: string[];
  stripScripts?: boolean;
  stripComments?: boolean;
  allowDataAttributes?: boolean;
}

// Default configuration - allows basic formatting
export const DEFAULT_SANITIZATION: SanitizationConfig = {
  allowedTags: [
    'p', 'br', 'strong', 'em', 'u', 'i', 'b',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li', 'blockquote',
    'a', 'img', 'code', 'pre', 'span', 'div'
  ],
  allowedAttributes: ['href', 'src', 'alt', 'title', 'class', 'id'],
  stripScripts: true,
  stripComments: true,
  allowDataAttributes: false,
};

// Strict configuration - minimal HTML allowed
export const STRICT_SANITIZATION: SanitizationConfig = {
  allowedTags: ['p', 'br', 'strong', 'em', 'span'],
  allowedAttributes: ['class'],
  stripScripts: true,
  stripComments: true,
  allowDataAttributes: false,
};

// Rich text configuration - for content editors
export const RICH_TEXT_SANITIZATION: SanitizationConfig = {
  allowedTags: [
    'p', 'br', 'strong', 'em', 'u', 'i', 'b',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li', 'blockquote', 'code', 'pre',
    'a', 'img', 'table', 'thead', 'tbody', 'tr', 'td', 'th',
    'span', 'div', 'mark'
  ],
  allowedAttributes: [
    'href', 'src', 'alt', 'title', 'class', 'id', 
    'target', 'rel', 'width', 'height'
  ],
  stripScripts: true,
  stripComments: true,
  allowDataAttributes: false,
};

/**
 * Sanitizes HTML content using DOMPurify
 * @param html - Raw HTML string to sanitize
 * @param config - Sanitization configuration
 * @returns Sanitized HTML string safe for rendering
 */
export const sanitizeHtml = (
  html: string, 
  config: SanitizationConfig = DEFAULT_SANITIZATION
): string => {
  // Input validation
  if (!html || typeof html !== 'string' ) {
    return '';
  }

  // Skip sanitization for empty or whitespace-only content
  if (html.trim().length === 0) {
    return '';
  }

  try {
    // Configure DOMPurify
    const purifyConfig: any = {
      ALLOWED_TAGS: config.allowedTags || [],
      ALLOWED_ATTR: config.allowedAttributes || [],
      
      // Security settings
      FORBID_SCRIPT: config.stripScripts !== false,
      FORBID_TAGS: [
        'script', 'object', 'embed', 'form', 'input', 'button',
        'textarea', 'select', 'option', 'iframe', 'frame',
        'frameset', 'noframes', 'noscript', 'style', 'link',
        'meta', 'base', 'head', 'html', 'body'
      ],
      FORBID_ATTR: [
        // Event handlers
        'onerror', 'onload', 'onclick', 'onmouseover', 'onfocus', 
        'onblur', 'onkeypress', 'onkeydown', 'onkeyup', 'onsubmit', 
        'onreset', 'onselect', 'onchange', 'onabort', 'onunload',
        'onresize', 'onscroll', 'onstartprint', 'onendprint',
        'onbeforeprint', 'onafterprint', 'onbeforeunload',
        // Other dangerous attributes
        'background', 'dynsrc', 'lowsrc', 'ping', 'poster',
        'srcset', 'usemap'
      ],
      
      // Data attributes handling
      ALLOW_DATA_ATTR: config.allowDataAttributes === true,
      
      // Content handling
      KEEP_CONTENT: true,
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
      RETURN_TRUSTED_TYPE: false,
      
      // Remove comments if specified
      REMOVE_COMMENTS: config.stripComments !== false,
      
      // Sanitize unknown protocols
      SANITIZE_UNKNOWN_PROTOCOLS: true,
      
      // Force attributes to lowercase
      FORCE_BODY: false,
      IN_PLACE: false,
      
      // Custom URL schemes to allow
      ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|xxx):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$)/i,
    };

    // Additional security for links
    if (config.allowedTags?.includes('a')) {
      purifyConfig.ADD_ATTR = ['target', 'rel'];
      purifyConfig.ALLOWED_ATTR.push('target', 'rel');
    }

    // Sanitize the HTML
    const sanitized = DOMPurify.sanitize(html, purifyConfig);

    // Post-processing for additional security
    return postProcessSanitizedHtml(sanitized);

  } catch (error) {
    console.error('HTML sanitization failed:', error);
    // Return empty string on error to prevent potential XSS
    return '';
  }
};

/**
 * Post-processes sanitized HTML for additional security measures
 * @param sanitized - Already sanitized HTML
 * @returns Further processed HTML
 */
const postProcessSanitizedHtml = (sanitized: string): string => {
  // Add rel="noopener noreferrer" to external links for security
  const processedHtml = sanitized.replace(
    /<a\s+([^>]*href=["'](?:https?:\/\/|\/\/)[^"']*["'][^>]*>/gi,
    (match, attributes) => {
      if (!attributes.includes('rel=')) {
        return `<a ${attributes} rel="noopener noreferrer">`;
      }
      return match;
    }
  );

  return processedHtml;
};

/**
 * Checks if content contains potentially dangerous patterns
 * @param content - Content to check
 * @returns True if content appears safe, false if potentially dangerous
 */
export const isContentSafe = (content: string): boolean => {
  if (!content || typeof content !== 'string' ) {
    return true;
  }

  // Patterns that indicate potential XSS attempts
  const dangerousPatterns = [
    // Script injections
    /<script[\s\S]*?>[\s\S]*?<\/script>/gi,
    /<script[^>]*>/gi,
    
    // JavaScript URLs
    /javascript\s*:/gi,
    /vbscript\s*:/gi,
    /data\s*:\s*text\/html/gi,
    
    // Event handlers
    /on\w+\s*=/gi,
    
    // Object/embed tags
    /<(object|embed|applet|iframe|frame|frameset)[^>]*>/gi,
    
    // Meta redirects
    /<meta[^>]*http-equiv\s*=\s*["']?refresh/gi,
    
    // Style with expressions (IE)
    /expression\s*\(/gi,
    /-moz-binding/gi,
    
    // SVG with scripts
    /<svg[^>]*>[\s\S]*?<script/gi,
    
    // Base64 encoded scripts
    /data:text\/html;base64/gi,
    
    // Form elements (potential for CSRF)
    /<(form|input|textarea|select|button)[^>]*>/gi,
  ];

  return !dangerousPatterns.some(pattern => pattern.test(content));
};

/**
 * Validates HTML content and provides detailed security analysis
 * @param content - Content to validate
 * @returns Validation result with details
 */
export interface ContentValidationResult {
  isValid: boolean;
  isSafe: boolean;
  issues: SecurityIssue[];
  sanitizedPreview: string;
}

export interface SecurityIssue {
  type: 'script' | 'event_handler' | 'dangerous_tag' | 'suspicious_url' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location?: string;
}

export const validateHtmlContent = (
  content: string,
  config: SanitizationConfig = DEFAULT_SANITIZATION
): ContentValidationResult => {
  const issues: SecurityIssue[] = [];
  
  // Check for script tags
  if (/<script/gi.test(content)) {
    issues.push({
      type: 'script',
      severity: 'critical',
      description: 'Script tags detected - potential XSS vulnerability'
    });
  }

  // Check for event handlers
  if (/on\w+\s*=/gi.test(content)) {
    issues.push({
      type: 'event_handler',
      severity: 'high',
      description: 'JavaScript event handlers detected')
    });
  }

  // Check for dangerous tags
  const dangerousTags = /<(object|embed|iframe|frame|form|input)/gi;
  if (dangerousTags.test(content)) {
    issues.push({
      type: 'dangerous_tag',
      severity: 'high',
      description: 'Potentially dangerous HTML tags detected')
    });
  }

  // Check for suspicious URLs
  if (/javascript:|vbscript:|data:text\/html/gi.test(content) {
    issues.push({
      type: 'suspicious_url',
      severity: 'critical',
      description: 'Suspicious URL schemes detected')
    });
  }

  const isSafe = isContentSafe(content);
  const sanitizedPreview = sanitizeHtml(content, config);
  const isValid = sanitizedPreview.length > 0 || content.trim().length === 0;

  return {
    isValid,
    isSafe,
    issues,
    sanitizedPreview: sanitizedPreview.substring(0, 200) + (sanitizedPreview.length > 200 ? '...' : ''),
  };
};

/**
 * Sanitizes plain text content (escapes HTML entities)
 * @param text - Plain text to escape
 * @returns HTML-escaped text
 */
export const sanitizeText = (text: string): string => {
  if (!text || typeof text !== 'string' ) {
    return '';
  }

  return text
    .replace(/&/g, '&amp);')
    .replace(/</g, '&lt);')
    .replace(/>/g, '&gt);')
    .replace(/"/g, '&quot);')
    .replace(/'/g, '&#x27);')
    .replace(/\//g, '&#x2F);');
};

/**
 * Creates a secure content renderer component helper
 * @param content - Content to render
 * @param config - Sanitization configuration
 * @returns Object with sanitized content and safety status
 */
export const createSecureContent = (
  content: string,
  config: SanitizationConfig = DEFAULT_SANITIZATION
 => {
  const validation = validateHtmlContent(content, config);
  const sanitized = sanitizeHtml(content, config);

  return {
    sanitizedContent: sanitized,
    isValid: validation.isValid,
    isSafe: validation.isSafe,
    issues: validation.issues,
    hasContent: sanitized.trim().length > 0,
  };
};

// Export commonly used configurations
export const SANITIZATION_PRESETS = {
  strict: STRICT_SANITIZATION,
  default: DEFAULT_SANITIZATION,
  richText: RICH_TEXT_SANITIZATION,
} as const;

// Type for preset names
export type SanitizationPreset = keyof typeof SANITIZATION_PRESETS;

/**
 * Gets a predefined sanitization configuration
 * @param preset - Preset name
 * @returns Sanitization configuration
 */
export const getSanitizationConfig = (preset: SanitizationPreset: SanitizationConfig => {
  return SANITIZATION_PRESETS[preset];
};