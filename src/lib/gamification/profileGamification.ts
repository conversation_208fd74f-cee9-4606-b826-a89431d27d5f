/**
 * Profile Completion Gamification System
 * 
 * Gamification engine that encourages profile completion through
 * achievements, points, badges, and progress tracking.
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

import { ProfileSection, UserProfile } from '../../types/profile'

export interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  category: AchievementCategory
  pointValue: number
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  requirements: AchievementRequirement[]
  unlockedAt?: Date
  progress?: number
  maxProgress?: number
}

export type AchievementCategory =
  | 'profile'
  | 'social'
  | 'engagement'
  | 'shopping'
  | 'community'
  | 'special'

export interface AchievementRequirement {
  type: 'profile_field' | 'action_count' | 'point_threshold' | 'time_based' | 'social_interaction'
  field?: string
  count?: number
  threshold?: number
  timeframe?: number
  description: string
}

export interface GamificationProgress {
  level: number
  currentXP: number
  nextLevelXP: number
  totalXP: number
  achievements: Achievement[]
  availableAchievements: Achievement[]
  recentUnlocks: Achievement[]
  streak: {
    current: number
    longest: number
    lastActivity: Date
  }
  completionStats: {
    profileCompletion: number
    sectionsCompleted: string[]
    totalSections: number
  }
}

export interface GamificationReward {
  type: 'points' | 'achievement' | 'badge' | 'unlock'
  value: number
  title: string
  description: string
  rarity?: string
}

/**
 * Profile Gamification Engine
 */
export class ProfileGamificationEngine {
  private static instance: ProfileGamificationEngine
  private achievements: Map<string, Achievement[]> = new Map()

  static getInstance(): ProfileGamificationEngine {
    if (!this.instance) {
      this.instance = new ProfileGamificationEngine()
    }
    return this.instance
  }

  /**
   * Initialize gamification for a user
   */
  async initializeGamification(profile: UserProfile): Promise<GamificationProgress> {
    const achievements = this.getAllAchievements()
    const unlockedAchievements = await this.checkUnlockedAchievements(profile, achievements)
    const availableAchievements = achievements.filter(a => !unlockedAchievements.find(u => u.id === a.id))

    const totalXP = this.calculateTotalXP(profile, unlockedAchievements)
    const level = this.calculateLevel(totalXP)
    
    const progress: GamificationProgress = {
      level,
      currentXP: totalXP,
      nextLevelXP: this.getXPForLevel(level + 1),
      totalXP,
      achievements: unlockedAchievements,
      availableAchievements: this.addProgressToAchievements(availableAchievements, profile),
      recentUnlocks: this.getRecentUnlocks(unlockedAchievements),
      streak: this.calculateStreak(profile),
      completionStats: this.calculateCompletionStats(profile)
    }

    return progress
  }

  /**
   * Check for newly unlocked achievements
   */
  async checkForNewAchievements(
    profile: UserProfile, 
    currentAchievements: Achievement[]
  ): Promise<{ newAchievements: Achievement[], rewards: GamificationReward[] }> {
    const allAchievements = this.getAllAchievements()
    const currentIds = new Set(currentAchievements.map(a => a.id))
    
    const newlyUnlocked: Achievement[] = []
    const rewards: GamificationReward[] = []

    for (const achievement of allAchievements) {
      if (!currentIds.has(achievement.id) && this.isAchievementUnlocked(achievement, profile)) {
        achievement.unlockedAt = new Date()
        newlyUnlocked.push(achievement)
        
        rewards.push({
          type: 'points',
          value: achievement.pointValue,
          title: `Achievement Unlocked: ${achievement.title}`,
          description: achievement.description,
          rarity: achievement.rarity
        })
      }
    }

    return { newAchievements: newlyUnlocked, rewards }
  }

  /**
   * Calculate rewards for completing profile sections
   */
  calculateSectionRewards(
    sectionName: string, 
    profile: UserProfile
  ): GamificationReward[] {
    const rewards: GamificationReward[] = []
    const basePoints = this.getSectionPointValue(sectionName)
    
    // Base completion reward
    rewards.push({
      type: 'points',
      value: basePoints,
      title: `Section Completed: ${sectionName}`,
      description: `You completed the ${sectionName} section of your profile!`
    })

    // Bonus for first-time completion
    const isFirstTime = !profile.profileCompletion?.completedSections?.includes(sectionName as unknown as ProfileSection)
    if (isFirstTime) {
      rewards.push({
        type: 'points',
        value: Math.floor(basePoints * 0.5),
        title: 'First Time Bonus!',
        description: 'Extra points for completing this section for the first time!'
      })
    }

    // Milestone rewards
    const completionPercentage = profile.profileCompletion?.percentage || 0
    if (completionPercentage === 100) {
      rewards.push({
        type: 'achievement',
        value: 500,
        title: 'Profile Master',
        description: 'You completed your entire profile! Welcome to the elite!',
        rarity: 'epic'
      })
    } else if (completionPercentage >= 75) {
      rewards.push({
        type: 'badge',
        value: 100,
        title: 'Profile Expert',
        description: 'Your profile is nearly complete!'
      })
    }

    return rewards
  }

  /**
   * Get all available achievements
   */
  private getAllAchievements(): Achievement[] {
    return [
      // Profile Completion Achievements
      {
        id: 'first-profile-photo',
        title: 'Picture Perfect',
        description: 'Upload your first profile photo',
        icon: '📷',
        category: 'profile',
        pointValue: 100,
        rarity: 'common',
        requirements: [
          {
            type: 'profile_field',
            field: 'avatar',
            description: 'Upload a profile photo'
          }
        ]
      },
      {
        id: 'bio-writer',
        title: 'Storyteller',
        description: 'Write your first bio',
        icon: '✍️',
        category: 'profile',
        pointValue: 75,
        rarity: 'common',
        requirements: [
          {
            type: 'profile_field',
            field: 'bio',
            description: 'Write a bio about yourself'
          }
        ]
      },
      {
        id: 'profile-complete',
        title: 'Profile Master',
        description: 'Complete 100% of your profile',
        icon: '🏆',
        category: 'profile',
        pointValue: 500,
        rarity: 'epic',
        requirements: [
          {
            type: 'profile_field',
            field: 'completion',
            threshold: 100,
            description: 'Complete all profile sections'
          }
        ]
      },
      {
        id: 'social-connector',
        title: 'Social Butterfly',
        description: 'Add social media links to your profile',
        icon: '🦋',
        category: 'social',
        pointValue: 150,
        rarity: 'common',
        requirements: [
          {
            type: 'profile_field',
            field: 'socialLinks',
            description: 'Add social media links'
          }
        ]
      },

      // Point-based Achievements
      {
        id: 'point-collector-100',
        title: 'Point Collector',
        description: 'Earn your first 100 points',
        icon: '💯',
        category: 'engagement',
        pointValue: 50,
        rarity: 'common',
        requirements: [
          {
            type: 'point_threshold',
            threshold: 100,
            description: 'Accumulate 100 points'
          }
        ]
      },
      {
        id: 'point-master-1000',
        title: 'Point Master',
        description: 'Reach 1,000 points milestone',
        icon: '🎯',
        category: 'engagement',
        pointValue: 200,
        rarity: 'rare',
        requirements: [
          {
            type: 'point_threshold',
            threshold: 1000,
            description: 'Accumulate 1,000 points'
          }
        ]
      },
      {
        id: 'point-legend-5000',
        title: 'Point Legend',
        description: 'Achieve legendary status with 5,000 points',
        icon: '👑',
        category: 'engagement',
        pointValue: 1000,
        rarity: 'legendary',
        requirements: [
          {
            type: 'point_threshold',
            threshold: 5000,
            description: 'Accumulate 5,000 points'
          }
        ]
      },

      // Time-based Achievements
      {
        id: 'early-adopter',
        title: 'Early Adopter',
        description: 'Complete profile within first week',
        icon: '🚀',
        category: 'special',
        pointValue: 300,
        rarity: 'rare',
        requirements: [
          {
            type: 'time_based',
            timeframe: 7, // days
            description: 'Complete profile within 7 days of signup'
          }
        ]
      },
      {
        id: 'veteran-member',
        title: 'Veteran Member',
        description: 'Active member for 30 days',
        icon: '🏅',
        category: 'special',
        pointValue: 250,
        rarity: 'rare',
        requirements: [
          {
            type: 'time_based',
            timeframe: 30,
            description: 'Be an active member for 30 days'
          }
        ]
      },

      // Social Achievements
      {
        id: 'community-joiner',
        title: 'Community Member',
        description: 'Join the Syndicaps community',
        icon: '🤝',
        category: 'community',
        pointValue: 100,
        rarity: 'common',
        requirements: [
          {
            type: 'social_interaction',
            count: 1,
            description: 'Join the community'
          }
        ]
      }
    ]
  }

  /**
   * Check if an achievement is unlocked for a profile
   */
  private isAchievementUnlocked(achievement: Achievement, profile: UserProfile): boolean {
    return achievement.requirements.every(req => {
      switch (req.type) {
        case 'profile_field':
          return this.checkProfileFieldRequirement(req, profile)
        case 'point_threshold':
          return profile.points >= (req.threshold || 0)
        case 'time_based':
          return this.checkTimeBasedRequirement(req, profile)
        case 'social_interaction':
          return this.checkSocialRequirement(req, profile)
        default:
          return false
      }
    })
  }

  private checkProfileFieldRequirement(req: AchievementRequirement, profile: UserProfile): boolean {
    switch (req.field) {
      case 'avatar':
        return !!profile.avatar
      case 'bio':
        return !!profile.bio && profile.bio.length > 10
      case 'completion':
        return (profile.profileCompletion?.percentage || 0) >= (req.threshold || 100)
      case 'socialLinks':
        return !!profile.socialLinks && Object.keys(profile.socialLinks).length > 0
      default:
        return false
    }
  }

  private checkTimeBasedRequirement(req: AchievementRequirement, profile: UserProfile): boolean {
    if (!req.timeframe) return false
    
    const createdAt = profile.createdAt instanceof Date 
      ? profile.createdAt 
      : (profile.createdAt as any).toDate()
    
    const daysSinceCreation = Math.floor((Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24))
    return daysSinceCreation >= req.timeframe
  }

  private checkSocialRequirement(req: AchievementRequirement, profile: UserProfile): boolean {
    // Mock implementation - in real app, this would check actual social interactions
    return profile.points > 0 // Assume any activity counts as joining community
  }

  private async checkUnlockedAchievements(
    profile: UserProfile, 
    achievements: Achievement[]
  ): Promise<Achievement[]> {
    return achievements.filter(achievement => this.isAchievementUnlocked(achievement, profile))
  }

  private addProgressToAchievements(achievements: Achievement[], profile: UserProfile): Achievement[] {
    return achievements.map(achievement => {
      const progress = this.calculateAchievementProgress(achievement, profile)
      return {
        ...achievement,
        progress: progress.current,
        maxProgress: progress.max
      }
    })
  }

  private calculateAchievementProgress(achievement: Achievement, profile: UserProfile): { current: number, max: number } {
    // Calculate progress for the first requirement (simplified)
    const req = achievement.requirements[0]
    
    switch (req.type) {
      case 'point_threshold':
        return {
          current: Math.min(profile.points, req.threshold || 0),
          max: req.threshold || 0
        }
      case 'profile_field':
        if (req.field === 'completion') {
          return {
            current: profile.profileCompletion?.percentage || 0,
            max: 100
          }
        }
        return { current: 0, max: 1 }
      default:
        return { current: 0, max: 1 }
    }
  }

  private calculateTotalXP(profile: UserProfile, achievements: Achievement[]): number {
    const baseXP = profile.points || 0
    const achievementXP = achievements.reduce((total, achievement) => total + achievement.pointValue, 0)
    return baseXP + achievementXP
  }

  private calculateLevel(xp: number): number {
    // Level progression: 100, 250, 500, 1000, 2000, 4000, etc.
    let level = 1
    let requiredXP = 100
    let totalRequired = 0
    while (xp >= totalRequired + requiredXP) {
      totalRequired += requiredXP
      level++
      requiredXP = Math.floor(requiredXP * 1.5)
    }

    return level
  }

  private getXPForLevel(level: number): number {
    let totalXP = 0
    let requiredXP = 100

    for (let i = 1; i < level; i++) {
      totalXP += requiredXP
      requiredXP = Math.floor(requiredXP * 1.5)
    }

    return totalXP
  }
  private getRecentUnlocks(achievements: Achievement[]): Achievement[] {
    const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
    return achievements
      .filter(a => a.unlockedAt && a.unlockedAt > threeDaysAgo)
      .sort((a, b) => (b.unlockedAt?.getTime() || 0) - (a.unlockedAt?.getTime() || 0))
      .slice(0, 5)
  }

  private calculateStreak(profile: UserProfile): { current: number, longest: number, lastActivity: Date } {
    // Mock calculation - in real app, this would track actual activity
    return {
      current: Math.floor(Math.random() * 7 + 1),
      longest: Math.floor(Math.random() * 30 + 5),
      lastActivity: new Date()
    }
  }

  private calculateCompletionStats(profile: UserProfile): {
    profileCompletion: number
    sectionsCompleted: string[]
    totalSections: number
  } {
    const totalSections = ['avatar', 'bio', 'personal', 'social', 'preferences']
    const completedSections = profile.profileCompletion?.completedSections || []
    
    return {
      profileCompletion: profile.profileCompletion?.percentage || 0,
      sectionsCompleted: completedSections.map(section => section.toString()),
      totalSections: totalSections.length
    }
  }

  private getSectionPointValue(sectionName: string): number {
    const pointValues: Record<string, number> = {
      avatar: 100,
      bio: 75,
      personal: 50,
      social: 125,
      preferences: 50
    }
    return pointValues[sectionName] || 25
  }
}

// Export singleton instance)
export const profileGamification = ProfileGamificationEngine.getInstance()