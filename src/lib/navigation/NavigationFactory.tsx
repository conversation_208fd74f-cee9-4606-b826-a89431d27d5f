/**
 * Navigation Component Factory
 * 
 * Factory pattern for generating navigation variants (desktop, mobile, admin)
 * from unified configuration. Reduces code duplication and ensures consistency.
 * 
 * Features:
 * - Single source of truth for navigation logic
 * - Variant-specific optimizations (desktop sidebar, mobile bottom nav)
 * - Shared state management through NavigationProvider
 * - Consistent styling and behavior across variants
 * - Accessibility compliance for all variants
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import React, { useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import { ChevronDown } from 'lucide-react'
import { useNavigation, NavigationItem, NavigationCategory } from './NavigationProvider'
import { UserProfile } from '@/types/profile'

// ===== TYPES =====

export type NavigationVariant = 'desktop' | 'mobile' | 'tablet' | 'admin'

export interface NavigationConfig {
  variant: NavigationVariant
  showSearch?: boolean
  showQuickSettings?: boolean
  showBreadcrumbs?: boolean
  showLabels?: boolean
  compact?: boolean
  maxItems?: number
  className?: string
}

export interface NavigationFactoryProps extends NavigationConfig {
  profile: UserProfile | null
  wishlistItemCount: number
  onNavigate?: (href: string) => void
}

// ===== SHARED COMPONENTS =====

/**
 * Navigation Item Component
 */
interface NavItemProps {
  item: NavigationItem
  isActive: boolean
  variant: NavigationVariant
  showLabel?: boolean
  onClick?: () => void
}

const NavItem: React.FC<NavItemProps> = ({ 
  item, 
  isActive, 
  variant, 
  showLabel = true, 
  onClick 
}) => {
  const isMobile = variant === 'mobile'
  const isCompact = variant === 'tablet'
  
  return (
    <Link
      href={item.href}
      onClick={onClick}
      className={`
        nav-item nav-tech-glow
        ${isActive ? 'nav-item-active' : 'nav-item-hover'}
        ${isMobile ? 'nav-mobile-item' : ''}
        ${isCompact ? 'nav-compact-item' : ''}
      `}
      aria-current={isActive ? 'page' : undefined}
      aria-label={`Navigate to ${item.label}: ${item.description}`}
      role="menuitem"
    >
      <item.icon
        size={isMobile ? 20 : 18}
        className={`nav-item-icon ${isActive ? 'text-accent-500' : 'text-gray-400'}`}
        aria-hidden="true"
      />
      
      {showLabel && (
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <span className={`nav-item-text font-medium truncate ${isMobile ? 'text-sm' : 'text-sm'}`}>
              {item.label}
            </span>
            {item.badge && (
              <span
                className={`
                  nav-badge
                  ${item.badge.type === 'notification' ? 'nav-badge-notification' :
                    item.badge.type === 'count' ? 'nav-badge-count' :
                    item.badge.type === 'info' ? 'nav-badge-info' :
                    'nav-badge-warning'
                  }
                `}
                aria-label={`${item.badge.count} ${item.badge.type}`}
              >
                {item.badge.count}
              </span>
            )}
          </div>
          {!isMobile && !isCompact && (
            <p className="nav-item-description text-xs text-gray-500 mt-0.5 truncate">
              {item.description}
            </p>
          )}
        </div>
      )}
    </Link>
  )
}

/**
 * Navigation Category Component with Collapsible Functionality
 */
interface NavCategoryProps {
  category: NavigationCategory
  variant: NavigationVariant
  showHeader?: boolean
  collapsible?: boolean
  children: React.ReactNode
}

const NavCategory: React.FC<NavCategoryProps> = ({
  category,
  variant,
  showHeader = true,
  collapsible = false,
  children
}) => {
  const { isActive } = useNavigation()
  const [isExpanded, setIsExpanded] = React.useState(false)
  const [hoverTimeout, setHoverTimeout] = React.useState<NodeJS.Timeout | null>(null)
  const isMobile = variant === 'mobile'

  // Check if any item in this category is currently active
  const hasActiveItem = React.useMemo(() => {
    return category.items.some(item => isActive(item.href))
  }, [category.items, isActive])

  // Auto-expand if category has an active item
  React.useEffect(() => {
    if (hasActiveItem && collapsible) {
      setIsExpanded(true)
      // Clear any pending collapse timeout when we have an active item
      if (hoverTimeout) {
        clearTimeout(hoverTimeout)
        setHoverTimeout(null)
      }
    }
  }, [hasActiveItem, collapsible, hoverTimeout])

  const handleMouseEnter = () => {
    if (!collapsible) return

    if (hoverTimeout) {
      clearTimeout(hoverTimeout)
      setHoverTimeout(null)
    }
    setIsExpanded(true)
  }

  const handleMouseLeave = () => {
    if (!collapsible) return

    // Don't collapse if we have an active item in this category
    if (hasActiveItem) return

    const timeout = setTimeout(() => {
      setIsExpanded(false)
    }, 300) // 300ms delay before collapsing
    setHoverTimeout(timeout)
  }

  React.useEffect(() => {
    return () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout)
      }
    }
  }, [hoverTimeout])

  if (isMobile) {
    return <div className="nav-mobile-category">{children}</div>
  }

  return (
    <div
      className={`nav-category ${collapsible ? 'nav-category-collapsible' : ''}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {showHeader && (
        <h3
          className={`nav-category-header ${collapsible ? 'nav-category-header-collapsible' : ''}`}
          aria-expanded={collapsible ? isExpanded : undefined}
          role={collapsible ? 'button' : undefined}
          tabIndex={collapsible ? 0 : undefined}
        >
          <category.icon
            size={16}
            className="nav-category-icon"
            aria-hidden="true"
          />
          <span className="nav-category-label">{category.label}</span>
          {category.badge && (
            <span className={`nav-badge nav-badge-${category.badge.type}`}>
              {category.badge.count}
            </span>
          )}
          {collapsible && (
            <ChevronDown
              size={14}
              className={`nav-category-chevron ${isExpanded ? 'nav-category-chevron-expanded' : ''}`}
              aria-hidden="true"
            />
          )}
        </h3>
      )}
      <div
        className={`nav-category-content ${collapsible ? 'nav-category-content-collapsible' : 'space-y-1'}`}
        style={collapsible ? {
          maxHeight: isExpanded ? '500px' : '0px',
          opacity: isExpanded ? 1 : 0
        } : undefined}
        role="group"
        aria-labelledby={`category-${category.id}`}
        aria-hidden={collapsible ? !isExpanded : undefined}
      >
        {children}
      </div>
    </div>
  )
}

// ===== VARIANT IMPLEMENTATIONS =====

/**
 * Desktop Sidebar Navigation
 */
const DesktopNavigation: React.FC<NavigationFactoryProps> = ({
  showSearch = true,
  showQuickSettings = true,
  showBreadcrumbs = false,
  className = '',
  onNavigate
}) => {
  const { state, isActive, navigate } = useNavigation()

  const handleNavigate = (href: string) => {
    onNavigate?.(href)
    navigate(href)
  }



  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className={`nav-container nav-desktop ${className}`}
      role="navigation"
      aria-label="Main navigation"
    >
      <div className="p-4 space-y-4">
        {/* Search and Quick Settings would go here */}

        {/* Navigation Categories - Collapsible */}
        <nav className="space-y-2" role="menu" aria-label="Profile navigation menu">)
          {state.categories.map((category) => (
            <NavCategory
              key={category.id}
              category={category}
              variant="desktop"
              showHeader={true}
              collapsible={true}
            >
              {category.items.map((item) => (
                <NavItem
                  key={item.id}
                  item={item}
                  isActive={isActive(item.href)}
                  variant="desktop"
                  showLabel={true}
                  onClick={() => handleNavigate(item.href)}
                />
              ))}
            </NavCategory>
          ))}
        </nav>
      </div>
    </motion.div>
  )
}

/**
 * Mobile Bottom Navigation
 */
const MobileNavigation: React.FC<NavigationFactoryProps> = ({
  showLabels = true,
  className = '',
  onNavigate
}) => {
  const { state, isActive, navigate } = useNavigation()
  
  const handleNavigate = (href: string) => {
    onNavigate?.(href)
    navigate(href)
  }
  
  return (
    <motion.nav
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className={`nav-mobile ${className}`}
      role="navigation"
      aria-label="Main navigation"
    >
      <div className="flex items-center justify-around px-2 py-2">)
        {state.categories.map((category) => {
          const isActiveCategory = category.items.some(item => isActive(item.href))
          const primaryItem = category.items[0] // Use first item as primary
          
          return (
            <NavItem
              key={category.id}
              item={{
                ...primaryItem,
                label: category.shortLabel || category.label,
                icon: category.icon,
                badge: category.badge
              }}
              isActive={isActiveCategory}
              variant="mobile"
              showLabel={showLabels}
              onClick={() => handleNavigate(primaryItem.href)}
            />
          )
        })}
      </div>
    </motion.nav>
  )
}

/**
 * Tablet Navigation (Hybrid)
 */
const TabletNavigation: React.FC<NavigationFactoryProps> = ({
  compact = true,
  className = '',
  onNavigate
}) => {
  const { state, isActive, navigate } = useNavigation()
  
  const handleNavigate = (href: string) => {
    onNavigate?.(href)
    navigate(href)
  }
  
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`nav-container nav-tablet ${className}`}
      role="navigation"
      aria-label="Main navigation"
    >
      <div className="p-3 space-y-3">
        <nav className="space-y-4" role="menu">)
          {state.categories.map((category) => (
            <NavCategory
              key={category.id}
              category={category}
              variant="tablet"
              showHeader={!compact}
            >
              {category.items.map((item) => (
                <NavItem
                  key={item.id}
                  item={item}
                  isActive={isActive(item.href)}
                  variant="tablet"
                  showLabel={true}
                  onClick={() => handleNavigate(item.href)}
                />
              ))}
            </NavCategory>
          ))}
        </nav>
      </div>
    </motion.div>
  )
}

// ===== FACTORY FUNCTION =====

/**
 * Navigation Factory - Creates navigation component based on variant
 */
export const createNavigation = (config: NavigationConfig) => {
  const NavigationComponent: React.FC<Omit<NavigationFactoryProps, keyof NavigationConfig>> = (props) => {
    const mergedProps = { ...config, ...props }
    
    switch (config.variant) {
      case 'desktop':
        return <DesktopNavigation {...mergedProps} />
      case 'mobile':
        return <MobileNavigation {...mergedProps} />
      case 'tablet':
        return <TabletNavigation {...mergedProps} />
      case 'admin':
        // Admin variant would be similar to desktop but with admin-specific items
        return <DesktopNavigation {...mergedProps} />
      default:
        return <DesktopNavigation {...mergedProps} />
    }
  }
  
  NavigationComponent.displayName = `Navigation${config.variant.charAt(0).toUpperCase() + config.variant.slice(1)}`
  
  return NavigationComponent
}

/**
 * Pre-configured navigation components
 */
export const DesktopSidebarNavigation = createNavigation({
  variant: 'desktop',
  showSearch: true,
  showQuickSettings: true,
  showBreadcrumbs: false
})

export const MobileBottomNavigation = createNavigation({
  variant: 'mobile',
  showLabels: true,
  compact: false
})

export const TabletCompactNavigation = createNavigation({
  variant: 'tablet',
  compact: true,
  showSearch: false
})

// ===== REGISTRY INTEGRATION =====

/**
 * Auto-register core navigation variants with the registry
 */
export const registerCoreVariants = () => {
  // This would be called by NavigationRegistry to register these components
  return {
    DesktopSidebarNavigation,
    MobileBottomNavigation,
    TabletCompactNavigation
  }
}

/**
 * Enhanced factory with registry support
 */
export const createNavigationWithRegistry = (
  variantId: string,
  config?: Partial<NavigationConfig>
) => {
  // This would integrate with NavigationRegistry
  // For now, fallback to basic factory
  const baseConfig = {
    variant: variantId.includes('mobile') ? 'mobile' as const :
             variantId.includes('tablet') ? 'tablet' as const :
             'desktop' as const,
    ...config
  }
  return createNavigation(baseConfig)
}

export default createNavigation
