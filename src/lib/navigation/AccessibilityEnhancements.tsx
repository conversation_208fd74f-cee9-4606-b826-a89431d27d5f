/**
 * Navigation Accessibility Enhancements
 * 
 * Comprehensive accessibility system for navigation components including
 * keyboard shortcuts, focus management, ARIA enhancements, and WCAG 2.1 AA compliance.
 * 
 * Features:
 * - Advanced keyboard navigation with customizable shortcuts
 * - Focus management and trap system
 * - Screen reader optimizations
 * - ARIA live regions and announcements
 * - Skip links and landmark navigation
 * - High contrast and reduced motion support
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

'use client'

import { useEffect, useCallback, useRef, useState } from 'react'

// ===== TYPES =====

export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  metaKey?: boolean
  action: () => void
  description: string
  category: 'navigation' | 'search' | 'suggestions' | 'general'
  enabled?: boolean
}

export interface FocusManagementOptions {
  trapFocus?: boolean
  restoreFocus?: boolean
  initialFocus?: string | HTMLElement
  skipLinks?: boolean
}

export interface AccessibilityState {
  announcements: string[]
  focusedElement: string | null
  keyboardNavigation: boolean
  screenReaderMode: boolean
  highContrastMode: boolean
  reducedMotion: boolean
  skipLinksVisible: boolean
}

export interface AriaLiveRegionProps {
  message: string
  priority: 'polite' | 'assertive'
  clearAfter?: number
}

// ===== KEYBOARD SHORTCUTS MANAGER =====

export class KeyboardShortcutsManager {
  private shortcuts: Map<string, KeyboardShortcut> = new Map()
  private enabled = true
  private listeners: Set<(event: KeyboardEvent) => void> = new Set()
  private isClient = false

  constructor() {
    this.handleKeyDown = this.handleKeyDown.bind(this)

    // Only add event listener on client side
    if (typeof window !== 'undefined' && typeof document !== 'undefined' ) {
      this.isClient = true
      document.addEventListener('keydown', this.handleKeyDown)
    }
  }

  /**
   * Register a keyboard shortcut
   */
  register(id: string, shortcut: KeyboardShortcut): void {
    this.shortcuts.set(id, shortcut)
  }

  /**
   * Unregister a keyboard shortcut
   */
  unregister(id: string): void {
    this.shortcuts.delete(id)
  }

  /**
   * Enable/disable all shortcuts
   */
  setEnabled(enabled: boolean): void {
    this.enabled = enabled
  }

  /**
   * Get all registered shortcuts grouped by category
   */
  getShortcutsByCategory(): Record<string, KeyboardShortcut[]> {
    const grouped: Record<string, KeyboardShortcut[]> = {}
    
    this.shortcuts.forEach((shortcut) => {
      if (!grouped[shortcut.category]) {
        grouped[shortcut.category] = []
      }
      grouped[shortcut.category].push(shortcut)
    })
    
    return grouped
  }

  /**
   * Handle keyboard events
   */
  private handleKeyDown(event: KeyboardEvent): void {
    if (!this.enabled) return

    // Check if we're in an input field
    const target = event.target as HTMLElement
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true' ) {
      // Only allow certain shortcuts in input fields
      if (!['Escape', 'Tab'].includes(event.key)) {
        return
      }
    }

    // Find matching shortcut
    for (const [id, shortcut] of this.shortcuts) {
      if (this.matchesShortcut(event, shortcut)) {
        event.preventDefault()
        event.stopPropagation()
        shortcut.action()
        break
      }
    }

    // Notify listeners
    this.listeners.forEach(listener => listener(event))
  }

  /**
   * Check if event matches shortcut
   */
  private matchesShortcut(event: KeyboardEvent, shortcut: KeyboardShortcut): boolean {
    if (shortcut.enabled === false) return false
    
    return (
      event.key.toLowerCase() === shortcut.key.toLowerCase() &&
      !!event.ctrlKey === !!shortcut.ctrlKey &&
      !!event.altKey === !!shortcut.altKey &&
      !!event.shiftKey === !!shortcut.shiftKey &&
      !!event.metaKey === !!shortcut.metaKey
    )
  }

  /**
   * Add event listener
   */
  addListener(listener: (event: KeyboardEvent) => void): void {
    this.listeners.add(listener)
  }

  /**
   * Remove event listener
   */
  removeListener(listener: (event: KeyboardEvent) => void): void {
    this.listeners.delete(listener)
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.isClient && typeof document !== 'undefined') {
      document.removeEventListener('keydown', this.handleKeyDown)
    }
    this.shortcuts.clear()
    this.listeners.clear()
  }
}

// ===== FOCUS MANAGEMENT =====

export class FocusManager {
  private focusStack: HTMLElement[] = []
  private trapContainer: HTMLElement | null = null
  private restoreElement: HTMLElement | null = null
  private isClient = typeof window !== 'undefined' && typeof document !== 'undefined'

  /**
   * Set focus trap within container
   */
  trapFocus(container: HTMLElement, options: FocusManagementOptions = {}): void {
    if (!this.isClient) return

    this.trapContainer = container

    if (options.restoreFocus) {
      this.restoreElement = document.activeElement as HTMLElement
    }

    // Set initial focus
    if (options.initialFocus) {
      const initialElement = typeof options.initialFocus === 'string'
        ? container.querySelector(options.initialFocus) as HTMLElement
        : options.initialFocus
      
      if (initialElement) {
        initialElement.focus()
      }
    }

    // Add event listeners
    container.addEventListener('keydown', this.handleTrapKeyDown)
  }

  /**
   * Release focus trap
   */
  releaseFocusTrap(): void {
    if (this.trapContainer) {
      this.trapContainer.removeEventListener('keydown', this.handleTrapKeyDown)
      this.trapContainer = null
    }

    if (this.restoreElement) {
      this.restoreElement.focus()
      this.restoreElement = null
    }
  }

  /**
   * Handle keydown in focus trap
   */
  private handleTrapKeyDown = (event: KeyboardEvent): void => {
    if (event.key !== 'Tab' || !this.trapContainer) return

    if (!this.isClient) return

    const focusableElements = this.getFocusableElements(this.trapContainer)
    if (focusableElements.length === 0) return

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]
    const currentElement = document.activeElement as HTMLElement

    if (event.shiftKey) {
      // Shift + Tab
      if (currentElement === firstElement) {
        event.preventDefault()
        lastElement.focus()
      }
    } else {
      // Tab
      if (currentElement === lastElement) {
        event.preventDefault()
        firstElement.focus()
      }
    }
  }

  /**
   * Get focusable elements within container
   */
  private getFocusableElements(container: HTMLElement): HTMLElement[] {
    const selector = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ')

    return Array.from(container.querySelectorAll(selector)) as HTMLElement[]
  }

  /**
   * Move focus to next/previous element
   */
  moveFocus(direction: 'next' | 'previous', container?: HTMLElement): void {
    if (!this.isClient) return

    const focusableElements = this.getFocusableElements(container || document.body)
    const currentIndex = focusableElements.indexOf(document.activeElement as HTMLElement)

    if (currentIndex === -1) return

    let nextIndex: number
    if (direction === 'next') {
      nextIndex = (currentIndex + 1) % focusableElements.length
    } else {
      nextIndex = currentIndex === 0 ? focusableElements.length - 1 : currentIndex - 1
    }

    focusableElements[nextIndex]?.focus()
  }
}

// ===== ARIA LIVE REGIONS =====

export class AriaLiveManager {
  private politeRegion: HTMLElement | null = null
  private assertiveRegion: HTMLElement | null = null
  private isClient = typeof window !== 'undefined' && typeof document !== 'undefined'

  constructor() {
    if (this.isClient) {
      this.createLiveRegions()
    }
  }

  /**
   * Create ARIA live regions
   */
  private createLiveRegions(): void {
    if (!this.isClient) return

    // Polite region
    this.politeRegion = document.createElement('div')
    this.politeRegion.setAttribute('aria-live', 'polite')
    this.politeRegion.setAttribute('aria-atomic', 'true')
    this.politeRegion.className = 'sr-only'
    document.body.appendChild(this.politeRegion)

    // Assertive region
    this.assertiveRegion = document.createElement('div')
    this.assertiveRegion.setAttribute('aria-live', 'assertive')
    this.assertiveRegion.setAttribute('aria-atomic', 'true')
    this.assertiveRegion.className = 'sr-only'
    document.body.appendChild(this.assertiveRegion)
  }

  /**
   * Announce message to screen readers
   */
  announce(message: string, priority: 'polite' | 'assertive' = 'polite', clearAfter = 1000): void {
    if (!this.isClient) return

    const region = priority === 'polite' ? this.politeRegion : this.assertiveRegion
    if (!region) return

    region.textContent = message

    if (clearAfter > 0) {
      setTimeout(() => {
        if (region) region.textContent = ''
      }, clearAfter)
    }
  }

  /**
   * Clear all announcements
   */
  clear(): void {
    if (!this.isClient) return

    if (this.politeRegion) this.politeRegion.textContent = ''
    if (this.assertiveRegion) this.assertiveRegion.textContent = ''
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (!this.isClient) return

    if (this.politeRegion) {
      document.body.removeChild(this.politeRegion)
      this.politeRegion = null
    }
    if (this.assertiveRegion) {
      document.body.removeChild(this.assertiveRegion)
      this.assertiveRegion = null
    }
  }
}

// ===== ACCESSIBILITY PREFERENCES =====

export class AccessibilityPreferences {
  private preferences: Map<string, any> = new Map()

  constructor() {
    this.loadPreferences()
    this.detectSystemPreferences()
  }

  /**
   * Load preferences from localStorage
   */
  private loadPreferences(): void {
    try {
      const stored = localStorage.getItem('syndicaps-accessibility-preferences')
      if (stored) {
        const parsed = JSON.parse(stored)
        Object.entries(parsed).forEach(([key, value]) => {
          this.preferences.set(key, value)
        })
      }
    } catch (error) {
      console.warn('Failed to load accessibility preferences:', error)
    }
  }

  /**
   * Detect system accessibility preferences
   */
  private detectSystemPreferences(): void {
    // High contrast mode
    if (window.matchMedia('(prefers-contrast: high)').matches) {
      this.preferences.set('highContrast', true)
    }

    // Reduced motion
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      this.preferences.set('reducedMotion', true)
    }

    // Color scheme
    if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      this.preferences.set('darkMode', true)
    }
  }

  /**
   * Get preference value
   */
  get(key: string): any {
    return this.preferences.get(key)
  }

  /**
   * Set preference value
   */
  set(key: string, value: any): void {
    this.preferences.set(key, value)
    this.savePreferences()
  }

  /**
   * Save preferences to localStorage
   */
  private savePreferences(): void {
    try {
      const obj = Object.fromEntries(this.preferences)
      localStorage.setItem('syndicaps-accessibility-preferences', JSON.stringify(obj))
    } catch (error) {
      console.warn('Failed to save accessibility preferences:', error)
    }
  }
}

// ===== SINGLETON INSTANCES =====

// Only create instances on client side to avoid SSR issues
const isClient = typeof window !== 'undefined' && typeof document !== 'undefined'

export const keyboardShortcutsManager = isClient ? new KeyboardShortcutsManager() : null
export const focusManager = isClient ? new FocusManager() : null
export const ariaLiveManager = isClient ? new AriaLiveManager() : null
export const accessibilityPreferences = isClient ? new AccessibilityPreferences() : null

// ===== CLEANUP =====

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    keyboardShortcutsManager?.destroy()
    ariaLiveManager?.destroy()
  })
}

export default {
  KeyboardShortcutsManager,
  FocusManager,
  AriaLiveManager,
  AccessibilityPreferences,
  keyboardShortcutsManager,
  focusManager,
  ariaLiveManager,
  accessibilityPreferences
}
