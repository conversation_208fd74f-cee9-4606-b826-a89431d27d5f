'use server';

import { db } from '@/lib/firebase/admin';
import { createAndSendPayPalInvoice } from '@/lib/paypal/invoicing';
import { FieldValue } from 'firebase-admin/firestore';
import { revalidatePath } from 'next/cache';

/**
 * Manually send PayPal invoice to a raffle winner
 */
export async function manuallyInvoiceWinner(raffleId: string, winnerEntryId: string) {
  try {
    // Get the winner entry
    const entryDoc = await db.collection('raffle_entries').doc(winnerEntryId).get();
    if (!entryDoc.exists ) {
      return { success: false, message: 'Winner entry not found' };
    }

    const entryData = entryDoc.data();
    if (!entryData) {
      return { success: false, message: 'Winner entry data is invalid' };
    }

    // Get the raffle data
    const raffleDoc = await db.collection('raffles').doc(raffleId).get();
    if (!raffleDoc.exists) {
      return { success: false, message: 'Raffle not found' };
    }

    const raffleData = raffleDoc.data();
    if (!raffleData) {
      return { success: false, message: 'Raffle data is invalid' };
    }

    // Parse winner name
    const [firstName, ...lastNameParts] = entryData.userName.split(' ');
    const lastName = lastNameParts.join(' ');

    // Create and send PayPal invoice
    const invoiceResult = await createAndSendPayPalInvoice(
      {
        firstName: firstName,
        lastName: lastName,
        email: entryData.userEmail,
      },
      {
        productName: raffleData.productName || 'Raffle Prize',
        price: raffleData.price || 50,
        shippingCost: entryData.shippingCost || 10,
      }
    );

    // Update the entry with invoice information
    await db.collection('raffle_entries').doc(winnerEntryId).update({
      paypalInvoiceId: invoiceResult.invoiceId,
      paypalInvoiceUrl: invoiceResult.invoiceUrl,
      paypalInvoiceStatus: 'SENT',
      updatedAt: FieldValue.serverTimestamp()
    });

    revalidatePath('/admin/raffles');
    
    return { 
      success: true, 
      message: `Invoice sent successfully to ${entryData.userEmail}` 
    };

  } catch (error: any) {
    console.error('Error manually invoicing winner:', error);
    return { 
      success: false, 
      message: error.message || 'Failed to send invoice' 
    };
  }
} 